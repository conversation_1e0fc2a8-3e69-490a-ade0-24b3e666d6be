{"version": 2, "dgSpecHash": "nV0Id94FRnk=", "success": false, "projectFilePath": "D:\\00 Crack\\src\\UniversalLicenseManager.Shell\\UniversalLicenseManager.Shell.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\dryioc.dll\\4.7.7\\dryioc.dll.4.7.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\materialdesigncolors\\2.1.4\\materialdesigncolors.2.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\materialdesignthemes\\4.9.0\\materialdesignthemes.4.9.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\1.1.0\\microsoft.netcore.platforms.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.targets\\1.1.0\\microsoft.netcore.targets.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.xaml.behaviors.wpf\\1.1.39\\microsoft.xaml.behaviors.wpf.1.1.39.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.3\\newtonsoft.json.13.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\prism.core\\8.1.97\\prism.core.8.1.97.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\prism.dryioc\\8.1.97\\prism.dryioc.8.1.97.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\prism.wpf\\8.1.97\\prism.wpf.8.1.97.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.codedom\\7.0.0\\system.codedom.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io\\4.3.0\\system.io.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.management\\7.0.0\\system.management.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection\\4.3.0\\system.reflection.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit.ilgeneration\\4.3.0\\system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.emit.lightweight\\4.3.0\\system.reflection.emit.lightweight.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.primitives\\4.3.0\\system.reflection.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime\\4.3.0\\system.runtime.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks\\4.3.0\\system.threading.tasks.4.3.0.nupkg.sha512"], "logs": [{"code": "NU1104", "level": "Error", "message": "找不到项目“D:\\00 Crack\\src\\UniversalLicenseManager.DeveloperTools\\UniversalLicenseManager.DeveloperTools.csproj”。请检查项目引用是否有效以及项目文件是否存在。", "projectPath": "D:\\00 Crack\\src\\UniversalLicenseManager.Shell\\UniversalLicenseManager.Shell.csproj", "filePath": "D:\\00 Crack\\src\\UniversalLicenseManager.Shell\\UniversalLicenseManager.Shell.csproj", "libraryId": "D:\\00 Crack\\src\\UniversalLicenseManager.DeveloperTools\\UniversalLicenseManager.DeveloperTools.csproj", "targetGraphs": ["net6.0-windows7.0"]}, {"code": "NU1104", "level": "Error", "message": "找不到项目“D:\\00 Crack\\src\\UniversalLicenseManager.LicenseGeneration\\UniversalLicenseManager.LicenseGeneration.csproj”。请检查项目引用是否有效以及项目文件是否存在。", "projectPath": "D:\\00 Crack\\src\\UniversalLicenseManager.Shell\\UniversalLicenseManager.Shell.csproj", "filePath": "D:\\00 Crack\\src\\UniversalLicenseManager.Shell\\UniversalLicenseManager.Shell.csproj", "libraryId": "D:\\00 Crack\\src\\UniversalLicenseManager.LicenseGeneration\\UniversalLicenseManager.LicenseGeneration.csproj", "targetGraphs": ["net6.0-windows7.0"]}, {"code": "NU1104", "level": "Error", "message": "找不到项目“D:\\00 Crack\\src\\UniversalLicenseManager.Configuration\\UniversalLicenseManager.Configuration.csproj”。请检查项目引用是否有效以及项目文件是否存在。", "projectPath": "D:\\00 Crack\\src\\UniversalLicenseManager.Shell\\UniversalLicenseManager.Shell.csproj", "filePath": "D:\\00 Crack\\src\\UniversalLicenseManager.Shell\\UniversalLicenseManager.Shell.csproj", "libraryId": "D:\\00 Crack\\src\\UniversalLicenseManager.Configuration\\UniversalLicenseManager.Configuration.csproj", "targetGraphs": ["net6.0-windows7.0"]}]}