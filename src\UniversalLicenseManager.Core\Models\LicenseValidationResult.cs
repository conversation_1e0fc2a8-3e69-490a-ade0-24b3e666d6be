using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace UniversalLicenseManager.Core.Models
{
    /// <summary>
    /// License验证结果
    /// 包含验证状态、错误信息和详细信息
    /// </summary>
    public class LicenseValidationResult
    {
        /// <summary>
        /// 验证是否成功
        /// </summary>
        [JsonProperty("isValid")]
        public bool IsValid { get; set; }

        /// <summary>
        /// 验证状态
        /// </summary>
        [JsonProperty("status")]
        public LicenseValidationStatus Status { get; set; }

        /// <summary>
        /// 错误代码
        /// </summary>
        [JsonProperty("errorCode")]
        public string ErrorCode { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        [JsonProperty("errorMessage")]
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 详细错误信息列表
        /// </summary>
        [JsonProperty("errors")]
        public List<ValidationError> Errors { get; set; } = new List<ValidationError>();

        /// <summary>
        /// 验证的License信息
        /// </summary>
        [JsonProperty("licenseInfo")]
        public LicenseInfo LicenseInfo { get; set; }

        /// <summary>
        /// 验证时间
        /// </summary>
        [JsonProperty("validationTime")]
        public DateTime ValidationTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 剩余天数（-1表示永久）
        /// </summary>
        [JsonProperty("remainingDays")]
        public int RemainingDays { get; set; }

        /// <summary>
        /// 已授权的功能列表
        /// </summary>
        [JsonProperty("authorizedFeatures")]
        public List<string> AuthorizedFeatures { get; set; } = new List<string>();

        /// <summary>
        /// 设备指纹匹配结果
        /// </summary>
        [JsonProperty("deviceFingerprintMatch")]
        public bool DeviceFingerprintMatch { get; set; }

        /// <summary>
        /// 验证的设备指纹
        /// </summary>
        [JsonProperty("deviceFingerprint")]
        public string DeviceFingerprint { get; set; }

        /// <summary>
        /// 额外的验证信息
        /// </summary>
        [JsonProperty("additionalInfo")]
        public Dictionary<string, object> AdditionalInfo { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// 构造函数
        /// </summary>
        public LicenseValidationResult()
        {
        }

        /// <summary>
        /// 创建成功的验证结果
        /// </summary>
        /// <param name="licenseInfo">License信息</param>
        /// <param name="remainingDays">剩余天数</param>
        /// <returns>验证结果</returns>
        public static LicenseValidationResult Success(LicenseInfo licenseInfo, int remainingDays = -1)
        {
            return new LicenseValidationResult
            {
                IsValid = true,
                Status = LicenseValidationStatus.Valid,
                LicenseInfo = licenseInfo,
                RemainingDays = remainingDays,
                AuthorizedFeatures = licenseInfo?.AuthorizedFeatures ?? new List<string>(),
                DeviceFingerprintMatch = true
            };
        }

        /// <summary>
        /// 创建失败的验证结果
        /// </summary>
        /// <param name="status">验证状态</param>
        /// <param name="errorCode">错误代码</param>
        /// <param name="errorMessage">错误消息</param>
        /// <returns>验证结果</returns>
        public static LicenseValidationResult Failure(LicenseValidationStatus status, string errorCode, string errorMessage)
        {
            return new LicenseValidationResult
            {
                IsValid = false,
                Status = status,
                ErrorCode = errorCode,
                ErrorMessage = errorMessage
            };
        }

        /// <summary>
        /// 添加验证错误
        /// </summary>
        /// <param name="error">验证错误</param>
        public void AddError(ValidationError error)
        {
            if (error != null)
            {
                Errors.Add(error);
                IsValid = false;
            }
        }

        /// <summary>
        /// 添加验证错误
        /// </summary>
        /// <param name="code">错误代码</param>
        /// <param name="message">错误消息</param>
        /// <param name="severity">严重程度</param>
        public void AddError(string code, string message, ValidationErrorSeverity severity = ValidationErrorSeverity.Error)
        {
            AddError(new ValidationError(code, message, severity));
        }

        /// <summary>
        /// 检查是否有指定严重程度的错误
        /// </summary>
        /// <param name="severity">严重程度</param>
        /// <returns>是否存在</returns>
        public bool HasErrorsOfSeverity(ValidationErrorSeverity severity)
        {
            return Errors.Exists(e => e.Severity == severity);
        }

        /// <summary>
        /// 获取所有错误消息
        /// </summary>
        /// <returns>错误消息列表</returns>
        public List<string> GetAllErrorMessages()
        {
            var messages = new List<string>();
            if (!string.IsNullOrEmpty(ErrorMessage))
            {
                messages.Add(ErrorMessage);
            }
            
            foreach (var error in Errors)
            {
                messages.Add(error.Message);
            }
            
            return messages;
        }

        /// <summary>
        /// 重写ToString方法
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return $"LicenseValidation: {Status} - {(IsValid ? "Valid" : ErrorMessage)}";
        }
    }

    /// <summary>
    /// License验证状态枚举
    /// </summary>
    public enum LicenseValidationStatus
    {
        /// <summary>
        /// 有效
        /// </summary>
        Valid,

        /// <summary>
        /// 无效的License格式
        /// </summary>
        InvalidFormat,

        /// <summary>
        /// 已过期
        /// </summary>
        Expired,

        /// <summary>
        /// 设备指纹不匹配
        /// </summary>
        DeviceMismatch,

        /// <summary>
        /// 签名验证失败
        /// </summary>
        InvalidSignature,

        /// <summary>
        /// License已被撤销
        /// </summary>
        Revoked,

        /// <summary>
        /// 超出设备数量限制
        /// </summary>
        DeviceLimitExceeded,

        /// <summary>
        /// 产品不匹配
        /// </summary>
        ProductMismatch,

        /// <summary>
        /// 版本不兼容
        /// </summary>
        VersionIncompatible,

        /// <summary>
        /// 未知错误
        /// </summary>
        Unknown
    }

    /// <summary>
    /// 验证错误
    /// </summary>
    public class ValidationError
    {
        /// <summary>
        /// 错误代码
        /// </summary>
        [JsonProperty("code")]
        public string Code { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        [JsonProperty("message")]
        public string Message { get; set; }

        /// <summary>
        /// 严重程度
        /// </summary>
        [JsonProperty("severity")]
        public ValidationErrorSeverity Severity { get; set; }

        /// <summary>
        /// 错误发生时间
        /// </summary>
        [JsonProperty("timestamp")]
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 相关字段或属性
        /// </summary>
        [JsonProperty("field")]
        public string Field { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public ValidationError()
        {
        }

        /// <summary>
        /// 带参数的构造函数
        /// </summary>
        /// <param name="code">错误代码</param>
        /// <param name="message">错误消息</param>
        /// <param name="severity">严重程度</param>
        public ValidationError(string code, string message, ValidationErrorSeverity severity = ValidationErrorSeverity.Error)
        {
            Code = code;
            Message = message;
            Severity = severity;
        }

        /// <summary>
        /// 重写ToString方法
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return $"[{Severity}] {Code}: {Message}";
        }
    }

    /// <summary>
    /// 验证错误严重程度
    /// </summary>
    public enum ValidationErrorSeverity
    {
        /// <summary>
        /// 信息
        /// </summary>
        Info,

        /// <summary>
        /// 警告
        /// </summary>
        Warning,

        /// <summary>
        /// 错误
        /// </summary>
        Error,

        /// <summary>
        /// 严重错误
        /// </summary>
        Critical
    }
}
