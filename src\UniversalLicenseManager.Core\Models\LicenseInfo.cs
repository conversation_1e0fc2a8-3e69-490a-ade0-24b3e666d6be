using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace UniversalLicenseManager.Core.Models
{
    /// <summary>
    /// License信息数据模型
    /// 包含软件授权的所有相关信息
    /// </summary>
    public class LicenseInfo
    {
        /// <summary>
        /// 唯一标识符
        /// </summary>
        [JsonProperty("id")]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 关联的设备指纹ID
        /// </summary>
        [JsonProperty("deviceFingerprintId")]
        public string DeviceFingerprintId { get; set; }

        /// <summary>
        /// License密钥
        /// </summary>
        [JsonProperty("licenseKey")]
        public string LicenseKey { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        [JsonProperty("productName")]
        public string ProductName { get; set; }

        /// <summary>
        /// 产品版本
        /// </summary>
        [JsonProperty("productVersion")]
        public string ProductVersion { get; set; }

        /// <summary>
        /// License类型（如：试用版、标准版、专业版）
        /// </summary>
        [JsonProperty("licenseType")]
        public string LicenseType { get; set; }

        /// <summary>
        /// 授权功能列表
        /// </summary>
        [JsonProperty("authorizedFeatures")]
        public List<string> AuthorizedFeatures { get; set; } = new List<string>();

        /// <summary>
        /// 颁发日期
        /// </summary>
        [JsonProperty("issueDate")]
        public DateTime IssueDate { get; set; } = DateTime.Now;

        /// <summary>
        /// 过期日期（null表示永久授权）
        /// </summary>
        [JsonProperty("expirationDate")]
        public DateTime? ExpirationDate { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        [JsonProperty("isActive")]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 加密数据
        /// </summary>
        [JsonProperty("encryptedData")]
        public string EncryptedData { get; set; }

        /// <summary>
        /// 最大允许设备数量
        /// </summary>
        [JsonProperty("maxDevices")]
        public int? MaxDevices { get; set; }

        /// <summary>
        /// 已使用设备数量
        /// </summary>
        [JsonProperty("usedDevices")]
        public int UsedDevices { get; set; } = 1;

        /// <summary>
        /// 客户信息
        /// </summary>
        [JsonProperty("customerInfo")]
        public CustomerInfo CustomerInfo { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        [JsonProperty("remarks")]
        public string Remarks { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [JsonProperty("createdAt")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 最后更新时间
        /// </summary>
        [JsonProperty("updatedAt")]
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 构造函数
        /// </summary>
        public LicenseInfo()
        {
        }

        /// <summary>
        /// 带参数的构造函数
        /// </summary>
        /// <param name="productName">产品名称</param>
        /// <param name="licenseType">License类型</param>
        public LicenseInfo(string productName, string licenseType)
        {
            ProductName = productName;
            LicenseType = licenseType;
        }

        /// <summary>
        /// 检查License是否已过期
        /// </summary>
        /// <returns>是否过期</returns>
        public bool IsExpired()
        {
            return ExpirationDate.HasValue && DateTime.Now > ExpirationDate.Value;
        }

        /// <summary>
        /// 检查License是否有效
        /// </summary>
        /// <returns>是否有效</returns>
        public bool IsValid()
        {
            return IsActive && !IsExpired() && !string.IsNullOrEmpty(LicenseKey);
        }

        /// <summary>
        /// 获取剩余天数
        /// </summary>
        /// <returns>剩余天数，-1表示永久授权</returns>
        public int GetRemainingDays()
        {
            if (!ExpirationDate.HasValue)
                return -1; // 永久授权

            var remaining = ExpirationDate.Value - DateTime.Now;
            return Math.Max(0, (int)remaining.TotalDays);
        }

        /// <summary>
        /// 检查是否包含指定功能
        /// </summary>
        /// <param name="featureName">功能名称</param>
        /// <returns>是否包含</returns>
        public bool HasFeature(string featureName)
        {
            return AuthorizedFeatures?.Contains(featureName) == true;
        }

        /// <summary>
        /// 重写ToString方法
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return $"License: {ProductName} ({LicenseType}) - {(IsValid() ? "有效" : "无效")}";
        }
    }

    /// <summary>
    /// 客户信息
    /// </summary>
    public class CustomerInfo
    {
        /// <summary>
        /// 客户名称
        /// </summary>
        [JsonProperty("name")]
        public string Name { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        [JsonProperty("company")]
        public string Company { get; set; }

        /// <summary>
        /// 邮箱地址
        /// </summary>
        [JsonProperty("email")]
        public string Email { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        [JsonProperty("phone")]
        public string Phone { get; set; }

        /// <summary>
        /// 地址
        /// </summary>
        [JsonProperty("address")]
        public string Address { get; set; }
    }
}
