using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace UniversalLicenseManager.Core.Services
{
    /// <summary>
    /// 配置服务实现
    /// 基于JSON文件的配置管理
    /// </summary>
    public class ConfigurationService : IConfigurationService
    {
        private readonly string _configFilePath;
        private readonly Dictionary<string, object> _configurations;
        private readonly object _lockObject = new object();

        /// <summary>
        /// 构造函数
        /// </summary>
        public ConfigurationService()
        {
            var configDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config");
            if (!Directory.Exists(configDirectory))
            {
                Directory.CreateDirectory(configDirectory);
            }
            
            _configFilePath = Path.Combine(configDirectory, "app.config.json");
            _configurations = new Dictionary<string, object>();
            
            // 异步加载配置
            _ = LoadAsync();
        }

        /// <summary>
        /// 获取配置值
        /// </summary>
        /// <typeparam name="T">配置值类型</typeparam>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        public T GetValue<T>(string key, T defaultValue = default(T))
        {
            lock (_lockObject)
            {
                if (_configurations.TryGetValue(key, out var value))
                {
                    try
                    {
                        if (value is T directValue)
                            return directValue;
                        
                        // 尝试转换类型
                        return (T)Convert.ChangeType(value, typeof(T));
                    }
                    catch
                    {
                        return defaultValue;
                    }
                }
                
                return defaultValue;
            }
        }

        /// <summary>
        /// 设置配置值
        /// </summary>
        /// <typeparam name="T">配置值类型</typeparam>
        /// <param name="key">配置键</param>
        /// <param name="value">配置值</param>
        public void SetValue<T>(string key, T value)
        {
            lock (_lockObject)
            {
                _configurations[key] = value;
            }
        }

        /// <summary>
        /// 检查配置键是否存在
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>是否存在</returns>
        public bool HasKey(string key)
        {
            lock (_lockObject)
            {
                return _configurations.ContainsKey(key);
            }
        }

        /// <summary>
        /// 删除配置项
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>是否成功</returns>
        public bool RemoveKey(string key)
        {
            lock (_lockObject)
            {
                return _configurations.Remove(key);
            }
        }

        /// <summary>
        /// 获取所有配置键
        /// </summary>
        /// <returns>配置键列表</returns>
        public List<string> GetAllKeys()
        {
            lock (_lockObject)
            {
                return new List<string>(_configurations.Keys);
            }
        }

        /// <summary>
        /// 保存配置到文件
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> SaveAsync()
        {
            try
            {
                Dictionary<string, object> configCopy;
                lock (_lockObject)
                {
                    configCopy = new Dictionary<string, object>(_configurations);
                }

                var json = JsonConvert.SerializeObject(configCopy, Formatting.Indented);
                await File.WriteAllTextAsync(_configFilePath, json);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 从文件加载配置
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> LoadAsync()
        {
            try
            {
                if (!File.Exists(_configFilePath))
                {
                    // 创建默认配置
                    SetDefaultConfigurations();
                    await SaveAsync();
                    return true;
                }

                var json = await File.ReadAllTextAsync(_configFilePath);
                var loadedConfigs = JsonConvert.DeserializeObject<Dictionary<string, object>>(json);

                if (loadedConfigs != null)
                {
                    lock (_lockObject)
                    {
                        _configurations.Clear();
                        foreach (var kvp in loadedConfigs)
                        {
                            _configurations[kvp.Key] = kvp.Value;
                        }
                    }
                }

                return true;
            }
            catch
            {
                // 加载失败时设置默认配置
                SetDefaultConfigurations();
                return false;
            }
        }

        /// <summary>
        /// 重置所有配置为默认值
        /// </summary>
        public void ResetToDefaults()
        {
            lock (_lockObject)
            {
                _configurations.Clear();
            }
            SetDefaultConfigurations();
        }

        /// <summary>
        /// 导出配置到指定文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否成功</returns>
        public async Task<bool> ExportAsync(string filePath)
        {
            try
            {
                Dictionary<string, object> configCopy;
                lock (_lockObject)
                {
                    configCopy = new Dictionary<string, object>(_configurations);
                }

                var json = JsonConvert.SerializeObject(configCopy, Formatting.Indented);
                await File.WriteAllTextAsync(filePath, json);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 从指定文件导入配置
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="overwrite">是否覆盖现有配置</param>
        /// <returns>是否成功</returns>
        public async Task<bool> ImportAsync(string filePath, bool overwrite = false)
        {
            try
            {
                if (!File.Exists(filePath))
                    return false;

                var json = await File.ReadAllTextAsync(filePath);
                var importedConfigs = JsonConvert.DeserializeObject<Dictionary<string, object>>(json);

                if (importedConfigs != null)
                {
                    lock (_lockObject)
                    {
                        if (overwrite)
                        {
                            _configurations.Clear();
                        }

                        foreach (var kvp in importedConfigs)
                        {
                            _configurations[kvp.Key] = kvp.Value;
                        }
                    }
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取配置文件路径
        /// </summary>
        /// <returns>配置文件路径</returns>
        public string GetConfigFilePath()
        {
            return _configFilePath;
        }

        /// <summary>
        /// 设置默认配置
        /// </summary>
        private void SetDefaultConfigurations()
        {
            SetValue("App.Name", "Universal License Manager");
            SetValue("App.Version", "1.0.0");
            SetValue("App.Language", "zh-CN");
            SetValue("Encryption.DefaultAlgorithm", "SHA256");
            SetValue("Encryption.RSAKeySize", 2048);
            SetValue("License.DefaultExpirationDays", 365);
            SetValue("UI.Theme", "Light");
            SetValue("Log.Level", "Info");
        }
    }
}
