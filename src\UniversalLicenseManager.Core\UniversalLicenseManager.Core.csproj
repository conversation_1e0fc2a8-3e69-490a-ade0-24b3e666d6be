<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <AssemblyTitle>Universal License Manager Core</AssemblyTitle>
    <AssemblyDescription>通用软件注册机核心业务逻辑模块</AssemblyDescription>
    <AssemblyCompany>Universal License Manager Team</AssemblyCompany>
    <AssemblyProduct>Universal License Manager</AssemblyProduct>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Prism.Core" Version="8.1.97" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Management" Version="7.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Models\" />
    <Folder Include="Services\" />
    <Folder Include="Encryption\" />
    <Folder Include="Utilities\" />
  </ItemGroup>

</Project>
