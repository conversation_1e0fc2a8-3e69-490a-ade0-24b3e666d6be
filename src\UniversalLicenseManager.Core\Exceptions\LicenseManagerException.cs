using System;

namespace UniversalLicenseManager.Core.Exceptions
{
    /// <summary>
    /// License Manager基础异常类
    /// </summary>
    public class LicenseManagerException : Exception
    {
        /// <summary>
        /// 错误代码
        /// </summary>
        public string ErrorCode { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public LicenseManagerException() : base()
        {
        }

        /// <summary>
        /// 带消息的构造函数
        /// </summary>
        /// <param name="message">错误消息</param>
        public LicenseManagerException(string message) : base(message)
        {
        }

        /// <summary>
        /// 带消息和内部异常的构造函数
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="innerException">内部异常</param>
        public LicenseManagerException(string message, Exception innerException) : base(message, innerException)
        {
        }

        /// <summary>
        /// 带错误代码和消息的构造函数
        /// </summary>
        /// <param name="errorCode">错误代码</param>
        /// <param name="message">错误消息</param>
        public LicenseManagerException(string errorCode, string message) : base(message)
        {
            ErrorCode = errorCode;
        }

        /// <summary>
        /// 带错误代码、消息和内部异常的构造函数
        /// </summary>
        /// <param name="errorCode">错误代码</param>
        /// <param name="message">错误消息</param>
        /// <param name="innerException">内部异常</param>
        public LicenseManagerException(string errorCode, string message, Exception innerException) : base(message, innerException)
        {
            ErrorCode = errorCode;
        }
    }

    /// <summary>
    /// License验证异常
    /// </summary>
    public class LicenseValidationException : LicenseManagerException
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="message">错误消息</param>
        public LicenseValidationException(string message) : base(message)
        {
        }

        /// <summary>
        /// 带错误代码的构造函数
        /// </summary>
        /// <param name="errorCode">错误代码</param>
        /// <param name="message">错误消息</param>
        public LicenseValidationException(string errorCode, string message) : base(errorCode, message)
        {
        }

        /// <summary>
        /// 带内部异常的构造函数
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="innerException">内部异常</param>
        public LicenseValidationException(string message, Exception innerException) : base(message, innerException)
        {
        }
    }

    /// <summary>
    /// 设备指纹异常
    /// </summary>
    public class DeviceFingerprintException : LicenseManagerException
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="message">错误消息</param>
        public DeviceFingerprintException(string message) : base(message)
        {
        }

        /// <summary>
        /// 带错误代码的构造函数
        /// </summary>
        /// <param name="errorCode">错误代码</param>
        /// <param name="message">错误消息</param>
        public DeviceFingerprintException(string errorCode, string message) : base(errorCode, message)
        {
        }

        /// <summary>
        /// 带内部异常的构造函数
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="innerException">内部异常</param>
        public DeviceFingerprintException(string message, Exception innerException) : base(message, innerException)
        {
        }
    }

    /// <summary>
    /// 加密异常
    /// </summary>
    public class EncryptionException : LicenseManagerException
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="message">错误消息</param>
        public EncryptionException(string message) : base(message)
        {
        }

        /// <summary>
        /// 带错误代码的构造函数
        /// </summary>
        /// <param name="errorCode">错误代码</param>
        /// <param name="message">错误消息</param>
        public EncryptionException(string errorCode, string message) : base(errorCode, message)
        {
        }

        /// <summary>
        /// 带内部异常的构造函数
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="innerException">内部异常</param>
        public EncryptionException(string message, Exception innerException) : base(message, innerException)
        {
        }
    }

    /// <summary>
    /// 配置异常
    /// </summary>
    public class ConfigurationException : LicenseManagerException
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="message">错误消息</param>
        public ConfigurationException(string message) : base(message)
        {
        }

        /// <summary>
        /// 带错误代码的构造函数
        /// </summary>
        /// <param name="errorCode">错误代码</param>
        /// <param name="message">错误消息</param>
        public ConfigurationException(string errorCode, string message) : base(errorCode, message)
        {
        }

        /// <summary>
        /// 带内部异常的构造函数
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="innerException">内部异常</param>
        public ConfigurationException(string message, Exception innerException) : base(message, innerException)
        {
        }
    }

    /// <summary>
    /// 文件操作异常
    /// </summary>
    public class FileOperationException : LicenseManagerException
    {
        /// <summary>
        /// 文件路径
        /// </summary>
        public string FilePath { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="message">错误消息</param>
        public FileOperationException(string message) : base(message)
        {
        }

        /// <summary>
        /// 带文件路径的构造函数
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="message">错误消息</param>
        public FileOperationException(string filePath, string message) : base(message)
        {
            FilePath = filePath;
        }

        /// <summary>
        /// 带内部异常的构造函数
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="message">错误消息</param>
        /// <param name="innerException">内部异常</param>
        public FileOperationException(string filePath, string message, Exception innerException) : base(message, innerException)
        {
            FilePath = filePath;
        }
    }

    /// <summary>
    /// 硬件访问异常
    /// </summary>
    public class HardwareAccessException : LicenseManagerException
    {
        /// <summary>
        /// 硬件组件名称
        /// </summary>
        public string ComponentName { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="componentName">硬件组件名称</param>
        /// <param name="message">错误消息</param>
        public HardwareAccessException(string componentName, string message) : base(message)
        {
            ComponentName = componentName;
        }

        /// <summary>
        /// 带内部异常的构造函数
        /// </summary>
        /// <param name="componentName">硬件组件名称</param>
        /// <param name="message">错误消息</param>
        /// <param name="innerException">内部异常</param>
        public HardwareAccessException(string componentName, string message, Exception innerException) : base(message, innerException)
        {
            ComponentName = componentName;
        }
    }
}
