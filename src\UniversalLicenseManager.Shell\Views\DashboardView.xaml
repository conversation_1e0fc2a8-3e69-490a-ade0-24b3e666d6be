<UserControl x:Class="UniversalLicenseManager.Shell.Views.DashboardView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:prism="http://prismlibrary.com/"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             prism:ViewModelLocator.AutoWireViewModel="True">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 页面标题 -->
        <TextBlock Grid.Row="0" 
                   Text="仪表板" 
                   Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                   Margin="0,0,0,20"/>

        <!-- 统计卡片区域 -->
        <Grid Grid.Row="1" Margin="0,0,0,30">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 设备指纹统计 -->
            <materialDesign:Card Grid.Column="0" Margin="0,0,10,0" Padding="20">
                <StackPanel>
                    <materialDesign:PackIcon Kind="Fingerprint" 
                                           Width="40" Height="40" 
                                           HorizontalAlignment="Center"
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                    <TextBlock Text="设备指纹" 
                             HorizontalAlignment="Center" 
                             Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                             Margin="0,10,0,5"/>
                    <TextBlock Text="{Binding DeviceFingerprintCount}" 
                             HorizontalAlignment="Center" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- License统计 -->
            <materialDesign:Card Grid.Column="1" Margin="5,0,5,0" Padding="20">
                <StackPanel>
                    <materialDesign:PackIcon Kind="Key" 
                                           Width="40" Height="40" 
                                           HorizontalAlignment="Center"
                                           Foreground="{DynamicResource SecondaryHueMidBrush}"/>
                    <TextBlock Text="License数量" 
                             HorizontalAlignment="Center" 
                             Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                             Margin="0,10,0,5"/>
                    <TextBlock Text="{Binding LicenseCount}" 
                             HorizontalAlignment="Center" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             Foreground="{DynamicResource SecondaryHueMidBrush}"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- 配置模板统计 -->
            <materialDesign:Card Grid.Column="2" Margin="5,0,5,0" Padding="20">
                <StackPanel>
                    <materialDesign:PackIcon Kind="Settings" 
                                           Width="40" Height="40" 
                                           HorizontalAlignment="Center"
                                           Foreground="{DynamicResource MaterialDesignValidationErrorBrush}"/>
                    <TextBlock Text="配置模板" 
                             HorizontalAlignment="Center" 
                             Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                             Margin="0,10,0,5"/>
                    <TextBlock Text="{Binding ConfigurationCount}" 
                             HorizontalAlignment="Center" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             Foreground="{DynamicResource MaterialDesignValidationErrorBrush}"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- 活跃License统计 -->
            <materialDesign:Card Grid.Column="3" Margin="10,0,0,0" Padding="20">
                <StackPanel>
                    <materialDesign:PackIcon Kind="CheckCircle" 
                                           Width="40" Height="40" 
                                           HorizontalAlignment="Center"
                                           Foreground="Green"/>
                    <TextBlock Text="活跃License" 
                             HorizontalAlignment="Center" 
                             Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                             Margin="0,10,0,5"/>
                    <TextBlock Text="{Binding ActiveLicenseCount}" 
                             HorizontalAlignment="Center" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                             Foreground="Green"/>
                </StackPanel>
            </materialDesign:Card>
        </Grid>

        <!-- 主要功能快捷入口 -->
        <Grid Grid.Row="2">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <TextBlock Grid.Row="0" 
                       Text="快捷操作" 
                       Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                       Margin="0,0,0,20"/>

            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 左侧功能区 -->
                <StackPanel Grid.Column="0" Margin="0,0,15,0">
                    <Button Style="{StaticResource MaterialDesignRaisedButton}"
                            Height="60" Margin="0,0,0,15"
                            Command="{Binding NavigateToDeviceFingerprintCommand}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Fingerprint" Width="24" Height="24" Margin="0,0,10,0"/>
                            <TextBlock Text="生成设备指纹" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <Button Style="{StaticResource MaterialDesignRaisedButton}"
                            Height="60" Margin="0,0,0,15"
                            Command="{Binding NavigateToLicenseGenerationCommand}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Key" Width="24" Height="24" Margin="0,0,10,0"/>
                            <TextBlock Text="生成License" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                </StackPanel>

                <!-- 右侧功能区 -->
                <StackPanel Grid.Column="1" Margin="15,0,0,0">
                    <Button Style="{StaticResource MaterialDesignRaisedButton}"
                            Height="60" Margin="0,0,0,15"
                            Command="{Binding NavigateToConfigurationCommand}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Settings" Width="24" Height="24" Margin="0,0,10,0"/>
                            <TextBlock Text="配置管理" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>

                    <Button Style="{StaticResource MaterialDesignRaisedButton}"
                            Height="60" Margin="0,0,0,15"
                            Command="{Binding NavigateToDeveloperToolsCommand}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="CodeBraces" Width="24" Height="24" Margin="0,0,10,0"/>
                            <TextBlock Text="开发者工具" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </Grid>
    </Grid>
</UserControl>
