using System.Windows;
using System.Windows.Controls;
using Prism.Regions;

namespace UniversalLicenseManager.Shell.Views
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// 注意：按照MVVM模式，此处只包含UI交互逻辑，不包含业务逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        private readonly IRegionManager _regionManager;

        public MainWindow(IRegionManager regionManager)
        {
            InitializeComponent();
            _regionManager = regionManager;
            
            // 设置默认选中第一个菜单项
            NavigationMenu.SelectedIndex = 0;
            
            // 导航到默认页面
            NavigateToDefaultView();
        }

        /// <summary>
        /// 导航到默认视图（仪表板）
        /// </summary>
        private void NavigateToDefaultView()
        {
            // 导航到仪表板视图
            _regionManager.RequestNavigate("MainContentRegion", "DashboardView");
        }

        /// <summary>
        /// 导航菜单选择变更事件
        /// </summary>
        private void NavigationMenu_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (NavigationMenu.SelectedItem is ListBoxItem selectedItem)
            {
                var textBlock = FindTextBlockInListBoxItem(selectedItem);
                if (textBlock != null)
                {
                    NavigateToView(textBlock.Text);
                }
            }
        }

        /// <summary>
        /// 根据菜单文本导航到对应视图
        /// </summary>
        /// <param name="menuText">菜单文本</param>
        private void NavigateToView(string menuText)
        {
            string viewName = menuText switch
            {
                "仪表板" => "DashboardView",
                "设备指纹管理" => "DeviceFingerprintView",
                "License生成" => "LicenseGenerationView",
                "授权配置" => "ConfigurationView",
                "开发者工具" => "DeveloperToolsView",
                _ => "DashboardView"
            };

            try
            {
                _regionManager.RequestNavigate("MainContentRegion", viewName);
                StatusText.Text = $"已切换到：{menuText}";
            }
            catch (System.Exception ex)
            {
                StatusText.Text = $"导航失败：{ex.Message}";
            }
        }

        /// <summary>
        /// 在ListBoxItem中查找TextBlock
        /// </summary>
        private TextBlock FindTextBlockInListBoxItem(ListBoxItem item)
        {
            if (item.Content is StackPanel stackPanel)
            {
                foreach (var child in stackPanel.Children)
                {
                    if (child is TextBlock textBlock)
                    {
                        return textBlock;
                    }
                }
            }
            return null;
        }

        /// <summary>
        /// 最小化按钮点击事件
        /// </summary>
        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            WindowState = WindowState.Minimized;
        }

        /// <summary>
        /// 最大化/还原按钮点击事件
        /// </summary>
        private void MaximizeButton_Click(object sender, RoutedEventArgs e)
        {
            WindowState = WindowState == WindowState.Maximized ? WindowState.Normal : WindowState.Maximized;
        }

        /// <summary>
        /// 关闭按钮点击事件
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            // 可以在这里添加关闭前的确认逻辑
            var result = MessageBox.Show("确定要退出应用程序吗？", "确认退出", 
                                       MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                Application.Current.Shutdown();
            }
        }

        /// <summary>
        /// 窗口加载完成事件
        /// </summary>
        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            StatusText.Text = "应用程序已启动";
        }
    }
}
