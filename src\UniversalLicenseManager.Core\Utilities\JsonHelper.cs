using System;
using System.IO;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace UniversalLicenseManager.Core.Utilities
{
    /// <summary>
    /// JSON序列化帮助类
    /// 提供统一的JSON序列化和反序列化功能
    /// </summary>
    public static class JsonHelper
    {
        /// <summary>
        /// 默认JSON序列化设置
        /// </summary>
        public static readonly JsonSerializerSettings DefaultSettings = new JsonSerializerSettings
        {
            Formatting = Formatting.Indented,
            NullValueHandling = NullValueHandling.Ignore,
            DateFormatHandling = DateFormatHandling.IsoDateFormat,
            DateTimeZoneHandling = DateTimeZoneHandling.Local,
            ContractResolver = new CamelCasePropertyNamesContractResolver(),
            DefaultValueHandling = DefaultValueHandling.Include
        };

        /// <summary>
        /// 紧凑JSON序列化设置（无缩进）
        /// </summary>
        public static readonly JsonSerializerSettings CompactSettings = new JsonSerializerSettings
        {
            Formatting = Formatting.None,
            NullValueHandling = NullValueHandling.Ignore,
            DateFormatHandling = DateFormatHandling.IsoDateFormat,
            DateTimeZoneHandling = DateTimeZoneHandling.Local,
            ContractResolver = new CamelCasePropertyNamesContractResolver(),
            DefaultValueHandling = DefaultValueHandling.Include
        };

        /// <summary>
        /// 序列化对象为JSON字符串
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="obj">要序列化的对象</param>
        /// <param name="useCompactFormat">是否使用紧凑格式</param>
        /// <returns>JSON字符串</returns>
        public static string Serialize<T>(T obj, bool useCompactFormat = false)
        {
            try
            {
                if (obj == null)
                    return null;

                var settings = useCompactFormat ? CompactSettings : DefaultSettings;
                return JsonConvert.SerializeObject(obj, settings);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"序列化对象失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 反序列化JSON字符串为对象
        /// </summary>
        /// <typeparam name="T">目标对象类型</typeparam>
        /// <param name="json">JSON字符串</param>
        /// <returns>反序列化的对象</returns>
        public static T Deserialize<T>(string json)
        {
            try
            {
                if (string.IsNullOrEmpty(json))
                    return default(T);

                return JsonConvert.DeserializeObject<T>(json, DefaultSettings);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"反序列化JSON失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 反序列化JSON字符串为指定类型的对象
        /// </summary>
        /// <param name="json">JSON字符串</param>
        /// <param name="type">目标类型</param>
        /// <returns>反序列化的对象</returns>
        public static object Deserialize(string json, Type type)
        {
            try
            {
                if (string.IsNullOrEmpty(json))
                    return null;

                return JsonConvert.DeserializeObject(json, type, DefaultSettings);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"反序列化JSON失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 尝试序列化对象为JSON字符串
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="obj">要序列化的对象</param>
        /// <param name="json">输出的JSON字符串</param>
        /// <param name="useCompactFormat">是否使用紧凑格式</param>
        /// <returns>是否成功</returns>
        public static bool TrySerialize<T>(T obj, out string json, bool useCompactFormat = false)
        {
            json = null;
            try
            {
                json = Serialize(obj, useCompactFormat);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 尝试反序列化JSON字符串为对象
        /// </summary>
        /// <typeparam name="T">目标对象类型</typeparam>
        /// <param name="json">JSON字符串</param>
        /// <param name="obj">输出的对象</param>
        /// <returns>是否成功</returns>
        public static bool TryDeserialize<T>(string json, out T obj)
        {
            obj = default(T);
            try
            {
                obj = Deserialize<T>(json);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 验证JSON字符串格式是否正确
        /// </summary>
        /// <param name="json">JSON字符串</param>
        /// <returns>是否为有效的JSON</returns>
        public static bool IsValidJson(string json)
        {
            if (string.IsNullOrWhiteSpace(json))
                return false;

            try
            {
                JsonConvert.DeserializeObject(json);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 格式化JSON字符串（美化输出）
        /// </summary>
        /// <param name="json">原始JSON字符串</param>
        /// <returns>格式化后的JSON字符串</returns>
        public static string FormatJson(string json)
        {
            try
            {
                if (string.IsNullOrEmpty(json))
                    return json;

                var obj = JsonConvert.DeserializeObject(json);
                return JsonConvert.SerializeObject(obj, Formatting.Indented);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"格式化JSON失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 压缩JSON字符串（移除空白字符）
        /// </summary>
        /// <param name="json">原始JSON字符串</param>
        /// <returns>压缩后的JSON字符串</returns>
        public static string CompressJson(string json)
        {
            try
            {
                if (string.IsNullOrEmpty(json))
                    return json;

                var obj = JsonConvert.DeserializeObject(json);
                return JsonConvert.SerializeObject(obj, Formatting.None);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"压缩JSON失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 深度克隆对象（通过JSON序列化/反序列化）
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="obj">要克隆的对象</param>
        /// <returns>克隆的对象</returns>
        public static T DeepClone<T>(T obj)
        {
            try
            {
                if (obj == null)
                    return default(T);

                var json = Serialize(obj);
                return Deserialize<T>(json);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"深度克隆对象失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 将对象保存为JSON文件
        /// </summary>
        /// <typeparam name="T">对象类型</typeparam>
        /// <param name="obj">要保存的对象</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="useCompactFormat">是否使用紧凑格式</param>
        /// <returns>是否成功</returns>
        public static bool SaveToFile<T>(T obj, string filePath, bool useCompactFormat = false)
        {
            try
            {
                var json = Serialize(obj, useCompactFormat);
                
                var directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                File.WriteAllText(filePath, json);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 从JSON文件加载对象
        /// </summary>
        /// <typeparam name="T">目标对象类型</typeparam>
        /// <param name="filePath">文件路径</param>
        /// <returns>加载的对象</returns>
        public static T LoadFromFile<T>(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                    return default(T);

                var json = File.ReadAllText(filePath);
                return Deserialize<T>(json);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"从文件加载JSON失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 尝试从JSON文件加载对象
        /// </summary>
        /// <typeparam name="T">目标对象类型</typeparam>
        /// <param name="filePath">文件路径</param>
        /// <param name="obj">输出的对象</param>
        /// <returns>是否成功</returns>
        public static bool TryLoadFromFile<T>(string filePath, out T obj)
        {
            obj = default(T);
            try
            {
                obj = LoadFromFile<T>(filePath);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 合并两个JSON对象
        /// </summary>
        /// <param name="originalJson">原始JSON</param>
        /// <param name="newJson">新JSON</param>
        /// <returns>合并后的JSON</returns>
        public static string MergeJson(string originalJson, string newJson)
        {
            try
            {
                if (string.IsNullOrEmpty(originalJson))
                    return newJson;
                
                if (string.IsNullOrEmpty(newJson))
                    return originalJson;

                var original = JsonConvert.DeserializeObject(originalJson) as Newtonsoft.Json.Linq.JObject;
                var newObj = JsonConvert.DeserializeObject(newJson) as Newtonsoft.Json.Linq.JObject;

                if (original != null && newObj != null)
                {
                    original.Merge(newObj, new JsonMergeSettings
                    {
                        MergeArrayHandling = MergeArrayHandling.Union
                    });

                    return JsonConvert.SerializeObject(original, DefaultSettings);
                }

                return originalJson;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"合并JSON失败: {ex.Message}", ex);
            }
        }
    }
}
