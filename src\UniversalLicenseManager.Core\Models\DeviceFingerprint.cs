using System;
using Newtonsoft.Json;

namespace UniversalLicenseManager.Core.Models
{
    /// <summary>
    /// 设备指纹数据模型
    /// 用于唯一标识设备的硬件信息组合
    /// </summary>
    public class DeviceFingerprint
    {
        /// <summary>
        /// 唯一标识符
        /// </summary>
        [JsonProperty("id")]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 设备名称
        /// </summary>
        [JsonProperty("deviceName")]
        public string DeviceName { get; set; }

        /// <summary>
        /// CPU序列号
        /// </summary>
        [JsonProperty("cpuId")]
        public string CpuId { get; set; }

        /// <summary>
        /// 主板序列号
        /// </summary>
        [JsonProperty("motherboardId")]
        public string MotherboardId { get; set; }

        /// <summary>
        /// 硬盘序列号
        /// </summary>
        [JsonProperty("hardDiskId")]
        public string HardDiskId { get; set; }

        /// <summary>
        /// MAC地址
        /// </summary>
        [JsonProperty("macAddress")]
        public string MacAddress { get; set; }

        /// <summary>
        /// 系统UUID
        /// </summary>
        [JsonProperty("systemUuid")]
        public string SystemUuid { get; set; }

        /// <summary>
        /// 指纹哈希值
        /// </summary>
        [JsonProperty("fingerprintHash")]
        public string FingerprintHash { get; set; }

        /// <summary>
        /// 哈希算法类型
        /// </summary>
        [JsonProperty("algorithm")]
        public string Algorithm { get; set; } = "SHA256";

        /// <summary>
        /// 创建时间
        /// </summary>
        [JsonProperty("createdAt")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 是否激活
        /// </summary>
        [JsonProperty("isActive")]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 备注信息
        /// </summary>
        [JsonProperty("remarks")]
        public string Remarks { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public DeviceFingerprint()
        {
        }

        /// <summary>
        /// 带参数的构造函数
        /// </summary>
        /// <param name="deviceName">设备名称</param>
        public DeviceFingerprint(string deviceName)
        {
            DeviceName = deviceName;
        }

        /// <summary>
        /// 生成设备指纹的字符串表示
        /// </summary>
        /// <returns>设备指纹字符串</returns>
        public string GenerateFingerprintString()
        {
            return $"{CpuId}|{MotherboardId}|{HardDiskId}|{MacAddress}|{SystemUuid}";
        }

        /// <summary>
        /// 验证设备指纹是否有效
        /// </summary>
        /// <returns>是否有效</returns>
        public bool IsValid()
        {
            return !string.IsNullOrEmpty(CpuId) || 
                   !string.IsNullOrEmpty(MotherboardId) || 
                   !string.IsNullOrEmpty(HardDiskId) || 
                   !string.IsNullOrEmpty(MacAddress) || 
                   !string.IsNullOrEmpty(SystemUuid);
        }

        /// <summary>
        /// 重写ToString方法
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return $"DeviceFingerprint: {DeviceName} ({FingerprintHash})";
        }
    }
}
