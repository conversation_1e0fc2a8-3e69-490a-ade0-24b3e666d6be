<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <AssemblyTitle>Universal License Manager</AssemblyTitle>
    <AssemblyDescription>通用软件注册机桌面应用程序</AssemblyDescription>
    <AssemblyCompany>Universal License Manager Team</AssemblyCompany>
    <AssemblyProduct>Universal License Manager</AssemblyProduct>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <ApplicationIcon>Resources\app.ico</ApplicationIcon>
    <StartupObject>UniversalLicenseManager.Shell.App</StartupObject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Prism.Wpf" Version="8.1.97" />
    <PackageReference Include="Prism.DryIoc" Version="8.1.97" />
    <PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
    <PackageReference Include="MaterialDesignColors" Version="2.1.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\UniversalLicenseManager.Core\UniversalLicenseManager.Core.csproj" />
    <ProjectReference Include="..\UniversalLicenseManager.DeviceFingerprint\UniversalLicenseManager.DeviceFingerprint.csproj" />
    <ProjectReference Include="..\UniversalLicenseManager.LicenseGeneration\UniversalLicenseManager.LicenseGeneration.csproj" />
    <ProjectReference Include="..\UniversalLicenseManager.Configuration\UniversalLicenseManager.Configuration.csproj" />
    <ProjectReference Include="..\UniversalLicenseManager.DeveloperTools\UniversalLicenseManager.DeveloperTools.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Resources\" />
    <Folder Include="ViewModels\" />
    <Folder Include="Views\" />
  </ItemGroup>

</Project>
