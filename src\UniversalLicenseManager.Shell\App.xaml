<prism:PrismApplication
    x:Class="UniversalLicenseManager.Shell.App"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:prism="http://prismlibrary.com/">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!--  Material Design 主题  -->
                <materialDesign:BundledTheme
                    BaseTheme="Light"
                    PrimaryColor="DeepPurple"
                    SecondaryColor="Lime" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />

                <!--  自定义样式  -->
                <ResourceDictionary>
                    <!--  主窗口样式  -->
                    <Style x:Key="MainWindowStyle" TargetType="Window">
                        <Setter Property="WindowStyle" Value="None" />
                        <Setter Property="AllowsTransparency" Value="True" />
                        <Setter Property="Background" Value="Transparent" />
                        <Setter Property="ResizeMode" Value="CanResize" />
                        <Setter Property="MinWidth" Value="1200" />
                        <Setter Property="MinHeight" Value="800" />
                        <Setter Property="Width" Value="1400" />
                        <Setter Property="Height" Value="900" />
                    </Style>

                    <!--  导航菜单样式  -->
                    <Style
                        x:Key="NavigationMenuStyle"
                        BasedOn="{StaticResource MaterialDesignNavigationPrimaryListBox}"
                        TargetType="ListBox">
                        <Setter Property="Background" Value="{DynamicResource MaterialDesignPaper}" />
                        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}" />
                        <Setter Property="Margin" Value="0,16,0,16" />
                    </Style>

                    <!--  内容区域样式  -->
                    <Style x:Key="ContentRegionStyle" TargetType="ContentControl">
                        <Setter Property="Background" Value="{DynamicResource MaterialDesignBackground}" />
                        <Setter Property="Margin" Value="16" />
                    </Style>

                    <!--  标题栏样式  -->
                    <Style x:Key="TitleBarStyle" TargetType="Grid">
                        <Setter Property="Background" Value="{DynamicResource PrimaryHueMidBrush}" />
                        <Setter Property="Height" Value="40" />
                    </Style>
                </ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</prism:PrismApplication>
