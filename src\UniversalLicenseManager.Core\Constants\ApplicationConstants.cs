namespace UniversalLicenseManager.Core.Constants
{
    /// <summary>
    /// 应用程序常量定义
    /// </summary>
    public static class ApplicationConstants
    {
        /// <summary>
        /// 应用程序信息
        /// </summary>
        public static class Application
        {
            public const string Name = "Universal License Manager";
            public const string Version = "1.0.0";
            public const string Company = "Universal License Manager Team";
            public const string Copyright = "Copyright © 2024 Universal License Manager Team";
            public const string Description = "通用软件注册机桌面应用程序";
        }

        /// <summary>
        /// 文件和目录路径
        /// </summary>
        public static class Paths
        {
            public const string ConfigDirectory = "Config";
            public const string LogDirectory = "Logs";
            public const string DataDirectory = "Data";
            public const string TemplatesDirectory = "Templates";
            public const string ExportDirectory = "Export";
            public const string BackupDirectory = "Backup";
            
            public const string ConfigFileName = "app.config.json";
            public const string LicenseDataFileName = "licenses.json";
            public const string DeviceFingerprintDataFileName = "fingerprints.json";
            public const string AuthorizationConfigFileName = "authorization.json";
        }

        /// <summary>
        /// 加密相关常量
        /// </summary>
        public static class Encryption
        {
            public const string DefaultHashAlgorithm = "SHA256";
            public const int DefaultRSAKeySize = 2048;
            public const int DefaultAESKeySize = 256;
            public const string DefaultEncryptionAlgorithm = "AES";
            
            // 错误代码
            public const string ErrorInvalidKey = "INVALID_KEY";
            public const string ErrorEncryptionFailed = "ENCRYPTION_FAILED";
            public const string ErrorDecryptionFailed = "DECRYPTION_FAILED";
            public const string ErrorInvalidSignature = "INVALID_SIGNATURE";
        }

        /// <summary>
        /// License相关常量
        /// </summary>
        public static class License
        {
            public const int DefaultExpirationDays = 365;
            public const int DefaultWarningDays = 30;
            public const int MaxDevicesDefault = 1;
            public const string DefaultLicenseType = "Standard";
            
            // License状态
            public const string StatusActive = "Active";
            public const string StatusExpired = "Expired";
            public const string StatusRevoked = "Revoked";
            public const string StatusInactive = "Inactive";
            
            // License文件扩展名
            public const string LicenseFileExtension = ".lic";
            public const string LicenseKeyFileExtension = ".key";
            public const string LicenseConfigFileExtension = ".json";
        }

        /// <summary>
        /// 设备指纹相关常量
        /// </summary>
        public static class DeviceFingerprint
        {
            // 硬件组件名称
            public const string ComponentCPU = "CPU";
            public const string ComponentMotherboard = "Motherboard";
            public const string ComponentHardDisk = "HardDisk";
            public const string ComponentMacAddress = "MacAddress";
            public const string ComponentSystemUUID = "SystemUUID";
            public const string ComponentBIOS = "BIOS";
            public const string ComponentMemory = "Memory";
            public const string ComponentComputerName = "ComputerName";
            
            // 默认值
            public const string UnknownValue = "UNKNOWN";
            public const string ErrorPrefix = "ERROR_";
            
            // 相似度阈值
            public const double SimilarityThreshold = 0.8;
            public const double HighSimilarityThreshold = 0.9;
        }

        /// <summary>
        /// UI相关常量
        /// </summary>
        public static class UI
        {
            // 主题
            public const string ThemeLight = "Light";
            public const string ThemeDark = "Dark";
            
            // 语言
            public const string LanguageChineseSimplified = "zh-CN";
            public const string LanguageEnglish = "en-US";
            
            // 窗口尺寸
            public const int MinWindowWidth = 1200;
            public const int MinWindowHeight = 800;
            public const int DefaultWindowWidth = 1400;
            public const int DefaultWindowHeight = 900;
        }

        /// <summary>
        /// 日志相关常量
        /// </summary>
        public static class Logging
        {
            public const string DefaultLogLevel = "Info";
            public const string LogFileNameFormat = "app_{0:yyyyMMdd}.log";
            public const int MaxLogFileSizeMB = 10;
            public const int MaxLogFileCount = 30;
            
            // 日志类别
            public const string CategoryGeneral = "General";
            public const string CategorySecurity = "Security";
            public const string CategoryLicense = "License";
            public const string CategoryDeviceFingerprint = "DeviceFingerprint";
            public const string CategoryConfiguration = "Configuration";
        }

        /// <summary>
        /// 验证相关常量
        /// </summary>
        public static class Validation
        {
            // 错误代码
            public const string ErrorCodeInvalidFormat = "INVALID_FORMAT";
            public const string ErrorCodeExpired = "EXPIRED";
            public const string ErrorCodeDeviceMismatch = "DEVICE_MISMATCH";
            public const string ErrorCodeInvalidSignature = "INVALID_SIGNATURE";
            public const string ErrorCodeRevoked = "REVOKED";
            public const string ErrorCodeDeviceLimitExceeded = "DEVICE_LIMIT_EXCEEDED";
            public const string ErrorCodeProductMismatch = "PRODUCT_MISMATCH";
            public const string ErrorCodeVersionIncompatible = "VERSION_INCOMPATIBLE";
            public const string ErrorCodeUnknown = "UNKNOWN";
            
            // 成功代码
            public const string SuccessCodeValid = "VALID";
        }

        /// <summary>
        /// 网络相关常量
        /// </summary>
        public static class Network
        {
            public const int DefaultTimeoutSeconds = 30;
            public const int DefaultRetryCount = 3;
            public const string UserAgent = "Universal License Manager/1.0";
        }

        /// <summary>
        /// 数据格式常量
        /// </summary>
        public static class DataFormat
        {
            public const string JSON = "JSON";
            public const string XML = "XML";
            public const string Binary = "Binary";
            public const string Base64 = "Base64";
            public const string Encrypted = "Encrypted";
        }

        /// <summary>
        /// 功能模块常量
        /// </summary>
        public static class Features
        {
            // 核心功能
            public const string CoreFeatures = "CoreFeatures";
            public const string BasicLicenseManagement = "BasicLicenseManagement";
            public const string DeviceFingerprintGeneration = "DeviceFingerprintGeneration";
            
            // 高级功能
            public const string AdvancedEncryption = "AdvancedEncryption";
            public const string BatchLicenseGeneration = "BatchLicenseGeneration";
            public const string LicenseAnalytics = "LicenseAnalytics";
            public const string CustomConfiguration = "CustomConfiguration";
            public const string DeveloperTools = "DeveloperTools";
            
            // 企业功能
            public const string EnterpriseManagement = "EnterpriseManagement";
            public const string CentralizedLicenseServer = "CentralizedLicenseServer";
            public const string AdvancedReporting = "AdvancedReporting";
            public const string APIIntegration = "APIIntegration";
        }

        /// <summary>
        /// 配置键常量
        /// </summary>
        public static class ConfigKeys
        {
            public const string AppName = "App.Name";
            public const string AppVersion = "App.Version";
            public const string AppLanguage = "App.Language";
            public const string UITheme = "UI.Theme";
            public const string LogLevel = "Log.Level";
            public const string EncryptionDefaultAlgorithm = "Encryption.DefaultAlgorithm";
            public const string EncryptionRSAKeySize = "Encryption.RSAKeySize";
            public const string LicenseDefaultExpirationDays = "License.DefaultExpirationDays";
            public const string DeviceFingerprintStrategy = "DeviceFingerprint.Strategy";
            public const string AutoBackupEnabled = "AutoBackup.Enabled";
            public const string AutoBackupIntervalHours = "AutoBackup.IntervalHours";
        }
    }
}
