using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UniversalLicenseManager.Core.Models;

namespace UniversalLicenseManager.Core.Services
{
    /// <summary>
    /// License生成选项
    /// </summary>
    public class LicenseGenerationOptions
    {
        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 产品版本
        /// </summary>
        public string ProductVersion { get; set; }

        /// <summary>
        /// License类型
        /// </summary>
        public string LicenseType { get; set; }

        /// <summary>
        /// 授权功能列表
        /// </summary>
        public List<string> AuthorizedFeatures { get; set; } = new List<string>();

        /// <summary>
        /// 过期时间（null表示永久）
        /// </summary>
        public DateTime? ExpirationDate { get; set; }

        /// <summary>
        /// 最大设备数量
        /// </summary>
        public int? MaxDevices { get; set; }

        /// <summary>
        /// 客户信息
        /// </summary>
        public CustomerInfo CustomerInfo { get; set; }

        /// <summary>
        /// 自定义数据
        /// </summary>
        public Dictionary<string, object> CustomData { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// License服务接口
    /// 提供License生成、验证和管理功能
    /// </summary>
    public interface ILicenseService
    {
        /// <summary>
        /// 生成License
        /// </summary>
        /// <param name="deviceFingerprint">设备指纹</param>
        /// <param name="options">生成选项</param>
        /// <param name="privateKey">私钥（用于签名）</param>
        /// <returns>License信息</returns>
        Task<LicenseInfo> GenerateLicenseAsync(DeviceFingerprint deviceFingerprint, LicenseGenerationOptions options, string privateKey);

        /// <summary>
        /// 验证License
        /// </summary>
        /// <param name="licenseKey">License密钥</param>
        /// <param name="deviceFingerprint">当前设备指纹</param>
        /// <param name="publicKey">公钥（用于验证签名）</param>
        /// <returns>验证结果</returns>
        Task<LicenseValidationResult> ValidateLicenseAsync(string licenseKey, DeviceFingerprint deviceFingerprint, string publicKey);

        /// <summary>
        /// 验证License文件
        /// </summary>
        /// <param name="licenseFilePath">License文件路径</param>
        /// <param name="deviceFingerprint">当前设备指纹</param>
        /// <param name="publicKey">公钥</param>
        /// <returns>验证结果</returns>
        Task<LicenseValidationResult> ValidateLicenseFileAsync(string licenseFilePath, DeviceFingerprint deviceFingerprint, string publicKey);

        /// <summary>
        /// 解析License密钥
        /// </summary>
        /// <param name="licenseKey">License密钥</param>
        /// <param name="privateKey">私钥（用于解密）</param>
        /// <returns>License信息</returns>
        Task<LicenseInfo> ParseLicenseKeyAsync(string licenseKey, string privateKey);

        /// <summary>
        /// 生成License文件
        /// </summary>
        /// <param name="licenseInfo">License信息</param>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否成功</returns>
        Task<bool> GenerateLicenseFileAsync(LicenseInfo licenseInfo, string filePath);

        /// <summary>
        /// 从文件加载License
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>License信息</returns>
        Task<LicenseInfo> LoadLicenseFromFileAsync(string filePath);

        /// <summary>
        /// 检查License是否即将过期
        /// </summary>
        /// <param name="licenseInfo">License信息</param>
        /// <param name="warningDays">提前警告天数</param>
        /// <returns>是否即将过期</returns>
        bool IsLicenseExpiringSoon(LicenseInfo licenseInfo, int warningDays = 30);

        /// <summary>
        /// 续期License
        /// </summary>
        /// <param name="licenseInfo">原License信息</param>
        /// <param name="newExpirationDate">新过期时间</param>
        /// <param name="privateKey">私钥</param>
        /// <returns>新的License信息</returns>
        Task<LicenseInfo> RenewLicenseAsync(LicenseInfo licenseInfo, DateTime newExpirationDate, string privateKey);

        /// <summary>
        /// 撤销License
        /// </summary>
        /// <param name="licenseId">License ID</param>
        /// <param name="reason">撤销原因</param>
        /// <returns>是否成功</returns>
        Task<bool> RevokeLicenseAsync(string licenseId, string reason);

        /// <summary>
        /// 检查License是否被撤销
        /// </summary>
        /// <param name="licenseId">License ID</param>
        /// <returns>是否被撤销</returns>
        Task<bool> IsLicenseRevokedAsync(string licenseId);

        /// <summary>
        /// 获取License使用统计
        /// </summary>
        /// <param name="licenseId">License ID</param>
        /// <returns>使用统计信息</returns>
        Task<LicenseUsageStatistics> GetLicenseUsageStatisticsAsync(string licenseId);

        /// <summary>
        /// 批量生成License
        /// </summary>
        /// <param name="deviceFingerprints">设备指纹列表</param>
        /// <param name="options">生成选项</param>
        /// <param name="privateKey">私钥</param>
        /// <returns>License信息列表</returns>
        Task<List<LicenseInfo>> GenerateBatchLicensesAsync(List<DeviceFingerprint> deviceFingerprints, LicenseGenerationOptions options, string privateKey);

        /// <summary>
        /// 导出License为不同格式
        /// </summary>
        /// <param name="licenseInfo">License信息</param>
        /// <param name="format">导出格式（JSON、XML、Binary等）</param>
        /// <returns>导出的数据</returns>
        Task<byte[]> ExportLicenseAsync(LicenseInfo licenseInfo, string format);

        /// <summary>
        /// 从不同格式导入License
        /// </summary>
        /// <param name="data">导入的数据</param>
        /// <param name="format">数据格式</param>
        /// <returns>License信息</returns>
        Task<LicenseInfo> ImportLicenseAsync(byte[] data, string format);

        /// <summary>
        /// 生成License激活码
        /// </summary>
        /// <param name="licenseInfo">License信息</param>
        /// <returns>激活码</returns>
        string GenerateActivationCode(LicenseInfo licenseInfo);

        /// <summary>
        /// 通过激活码激活License
        /// </summary>
        /// <param name="activationCode">激活码</param>
        /// <param name="deviceFingerprint">设备指纹</param>
        /// <returns>激活结果</returns>
        Task<LicenseValidationResult> ActivateLicenseAsync(string activationCode, DeviceFingerprint deviceFingerprint);
    }

    /// <summary>
    /// License使用统计
    /// </summary>
    public class LicenseUsageStatistics
    {
        /// <summary>
        /// License ID
        /// </summary>
        public string LicenseId { get; set; }

        /// <summary>
        /// 首次使用时间
        /// </summary>
        public DateTime? FirstUsed { get; set; }

        /// <summary>
        /// 最后使用时间
        /// </summary>
        public DateTime? LastUsed { get; set; }

        /// <summary>
        /// 使用次数
        /// </summary>
        public int UsageCount { get; set; }

        /// <summary>
        /// 使用的设备数量
        /// </summary>
        public int DeviceCount { get; set; }

        /// <summary>
        /// 使用的设备列表
        /// </summary>
        public List<string> UsedDevices { get; set; } = new List<string>();

        /// <summary>
        /// 使用的功能统计
        /// </summary>
        public Dictionary<string, int> FeatureUsage { get; set; } = new Dictionary<string, int>();

        /// <summary>
        /// 总使用时长（分钟）
        /// </summary>
        public long TotalUsageMinutes { get; set; }

        /// <summary>
        /// 平均每日使用时长（分钟）
        /// </summary>
        public double AverageDailyUsageMinutes { get; set; }
    }
}
