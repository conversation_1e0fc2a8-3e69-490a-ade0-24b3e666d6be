using Prism.Commands;
using Prism.Mvvm;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using UniversalLicenseManager.Core.Models;
using UniversalLicenseManager.Core.Services;
using UniversalLicenseManager.DeviceFingerprint.Services;

namespace UniversalLicenseManager.DeviceFingerprint.ViewModels
{
    /// <summary>
    /// 设备指纹管理ViewModel
    /// 负责设备指纹生成、管理和验证的业务逻辑
    /// </summary>
    public class DeviceFingerprintViewModel : BindableBase
    {
        private readonly IDeviceFingerprintService _deviceFingerprintService;
        private readonly IFileService _fileService;
        private readonly ILogService _logService;

        #region 属性

        private ObservableCollection<Core.Models.DeviceFingerprint> _fingerprints;
        /// <summary>
        /// 设备指纹列表
        /// </summary>
        public ObservableCollection<Core.Models.DeviceFingerprint> Fingerprints
        {
            get { return _fingerprints; }
            set { SetProperty(ref _fingerprints, value); }
        }

        private Core.Models.DeviceFingerprint _selectedFingerprint;
        /// <summary>
        /// 选中的设备指纹
        /// </summary>
        public Core.Models.DeviceFingerprint SelectedFingerprint
        {
            get { return _selectedFingerprint; }
            set { SetProperty(ref _selectedFingerprint, value); }
        }

        private Core.Models.DeviceFingerprint _currentDeviceFingerprint;
        /// <summary>
        /// 当前设备指纹
        /// </summary>
        public Core.Models.DeviceFingerprint CurrentDeviceFingerprint
        {
            get { return _currentDeviceFingerprint; }
            set { SetProperty(ref _currentDeviceFingerprint, value); }
        }

        private FingerprintStrategy _selectedStrategy = FingerprintStrategy.AllHardware;
        /// <summary>
        /// 选中的指纹生成策略
        /// </summary>
        public FingerprintStrategy SelectedStrategy
        {
            get { return _selectedStrategy; }
            set { SetProperty(ref _selectedStrategy, value); }
        }

        private string _selectedAlgorithm = "SHA256";
        /// <summary>
        /// 选中的哈希算法
        /// </summary>
        public string SelectedAlgorithm
        {
            get { return _selectedAlgorithm; }
            set { SetProperty(ref _selectedAlgorithm, value); }
        }

        private List<string> _customComponents;
        /// <summary>
        /// 自定义硬件组件列表
        /// </summary>
        public List<string> CustomComponents
        {
            get { return _customComponents; }
            set { SetProperty(ref _customComponents, value); }
        }

        private List<string> _availableComponents;
        /// <summary>
        /// 可用硬件组件列表
        /// </summary>
        public List<string> AvailableComponents
        {
            get { return _availableComponents; }
            set { SetProperty(ref _availableComponents, value); }
        }

        private bool _isLoading;
        /// <summary>
        /// 是否正在加载
        /// </summary>
        public bool IsLoading
        {
            get { return _isLoading; }
            set { SetProperty(ref _isLoading, value); }
        }

        private string _statusMessage;
        /// <summary>
        /// 状态消息
        /// </summary>
        public string StatusMessage
        {
            get { return _statusMessage; }
            set { SetProperty(ref _statusMessage, value); }
        }

        private string _fingerprintReport;
        /// <summary>
        /// 设备指纹报告
        /// </summary>
        public string FingerprintReport
        {
            get { return _fingerprintReport; }
            set { SetProperty(ref _fingerprintReport, value); }
        }

        /// <summary>
        /// 可用的指纹生成策略
        /// </summary>
        public List<FingerprintStrategy> AvailableStrategies { get; }

        /// <summary>
        /// 可用的哈希算法
        /// </summary>
        public List<string> AvailableAlgorithms { get; }

        #endregion

        #region 命令

        /// <summary>
        /// 生成当前设备指纹命令
        /// </summary>
        public ICommand GenerateCurrentFingerprintCommand { get; private set; }

        /// <summary>
        /// 生成自定义指纹命令
        /// </summary>
        public ICommand GenerateCustomFingerprintCommand { get; private set; }

        /// <summary>
        /// 验证指纹命令
        /// </summary>
        public ICommand ValidateFingerprintCommand { get; private set; }

        /// <summary>
        /// 删除指纹命令
        /// </summary>
        public ICommand DeleteFingerprintCommand { get; private set; }

        /// <summary>
        /// 导出指纹命令
        /// </summary>
        public ICommand ExportFingerprintCommand { get; private set; }

        /// <summary>
        /// 导入指纹命令
        /// </summary>
        public ICommand ImportFingerprintCommand { get; private set; }

        /// <summary>
        /// 生成报告命令
        /// </summary>
        public ICommand GenerateReportCommand { get; private set; }

        /// <summary>
        /// 刷新可用组件命令
        /// </summary>
        public ICommand RefreshComponentsCommand { get; private set; }

        /// <summary>
        /// 清空列表命令
        /// </summary>
        public ICommand ClearListCommand { get; private set; }

        #endregion

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="deviceFingerprintService">设备指纹服务</param>
        /// <param name="fileService">文件服务</param>
        /// <param name="logService">日志服务</param>
        public DeviceFingerprintViewModel(
            IDeviceFingerprintService deviceFingerprintService,
            IFileService fileService,
            ILogService logService)
        {
            _deviceFingerprintService = deviceFingerprintService ?? throw new ArgumentNullException(nameof(deviceFingerprintService));
            _fileService = fileService ?? throw new ArgumentNullException(nameof(fileService));
            _logService = logService ?? throw new ArgumentNullException(nameof(logService));

            // 初始化集合
            Fingerprints = new ObservableCollection<Core.Models.DeviceFingerprint>();
            CustomComponents = new List<string>();

            // 初始化可用选项
            AvailableStrategies = Enum.GetValues<FingerprintStrategy>().ToList();
            AvailableAlgorithms = new List<string> { "MD5", "SHA1", "SHA256", "SHA512" };

            // 初始化命令
            InitializeCommands();

            // 异步初始化
            _ = InitializeAsync();
        }

        /// <summary>
        /// 初始化命令
        /// </summary>
        private void InitializeCommands()
        {
            GenerateCurrentFingerprintCommand = new DelegateCommand(async () => await GenerateCurrentFingerprintAsync(), () => !IsLoading);
            GenerateCustomFingerprintCommand = new DelegateCommand(async () => await GenerateCustomFingerprintAsync(), () => !IsLoading && CustomComponents?.Any() == true);
            ValidateFingerprintCommand = new DelegateCommand(async () => await ValidateFingerprintAsync(), () => !IsLoading && SelectedFingerprint != null);
            DeleteFingerprintCommand = new DelegateCommand(DeleteFingerprint, () => SelectedFingerprint != null);
            ExportFingerprintCommand = new DelegateCommand(async () => await ExportFingerprintAsync(), () => SelectedFingerprint != null);
            ImportFingerprintCommand = new DelegateCommand(async () => await ImportFingerprintAsync(), () => !IsLoading);
            GenerateReportCommand = new DelegateCommand(async () => await GenerateReportAsync(), () => SelectedFingerprint != null);
            RefreshComponentsCommand = new DelegateCommand(async () => await RefreshAvailableComponentsAsync(), () => !IsLoading);
            ClearListCommand = new DelegateCommand(ClearFingerprintList, () => Fingerprints?.Any() == true);
        }

        /// <summary>
        /// 异步初始化
        /// </summary>
        private async Task InitializeAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "正在初始化...";

                // 刷新可用硬件组件
                await RefreshAvailableComponentsAsync();

                StatusMessage = "初始化完成";
            }
            catch (Exception ex)
            {
                _logService.Error("初始化设备指纹模块失败", ex);
                StatusMessage = $"初始化失败: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
                UpdateCommandStates();
            }
        }

        /// <summary>
        /// 生成当前设备指纹
        /// </summary>
        private async Task GenerateCurrentFingerprintAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "正在生成当前设备指纹...";

                var fingerprint = await _deviceFingerprintService.GenerateFingerprintAsync(SelectedStrategy, SelectedAlgorithm);
                fingerprint.Remarks = $"使用策略 {SelectedStrategy} 生成";

                Fingerprints.Add(fingerprint);
                CurrentDeviceFingerprint = fingerprint;
                SelectedFingerprint = fingerprint;

                StatusMessage = $"设备指纹生成成功: {fingerprint.FingerprintHash}";
                _logService.Info($"生成设备指纹成功: {fingerprint.Id}");
            }
            catch (Exception ex)
            {
                _logService.Error("生成当前设备指纹失败", ex);
                StatusMessage = $"生成失败: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
                UpdateCommandStates();
            }
        }

        /// <summary>
        /// 生成自定义设备指纹
        /// </summary>
        private async Task GenerateCustomFingerprintAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "正在生成自定义设备指纹...";

                var fingerprint = await _deviceFingerprintService.GenerateCustomFingerprintAsync(CustomComponents, SelectedAlgorithm);
                fingerprint.Remarks = $"使用自定义组件 [{string.Join(", ", CustomComponents)}] 生成";

                Fingerprints.Add(fingerprint);
                SelectedFingerprint = fingerprint;

                StatusMessage = $"自定义设备指纹生成成功: {fingerprint.FingerprintHash}";
                _logService.Info($"生成自定义设备指纹成功: {fingerprint.Id}");
            }
            catch (Exception ex)
            {
                _logService.Error("生成自定义设备指纹失败", ex);
                StatusMessage = $"生成失败: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
                UpdateCommandStates();
            }
        }

        /// <summary>
        /// 验证设备指纹
        /// </summary>
        private async Task ValidateFingerprintAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "正在验证设备指纹...";

                var isValid = await _deviceFingerprintService.ValidateFingerprintAsync(SelectedFingerprint, SelectedStrategy);
                
                StatusMessage = $"验证结果: {(isValid ? "匹配当前设备" : "与当前设备不匹配")}";
                _logService.Info($"验证设备指纹 {SelectedFingerprint.Id}: {(isValid ? "匹配" : "不匹配")}");
            }
            catch (Exception ex)
            {
                _logService.Error("验证设备指纹失败", ex);
                StatusMessage = $"验证失败: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
                UpdateCommandStates();
            }
        }

        /// <summary>
        /// 删除设备指纹
        /// </summary>
        private void DeleteFingerprint()
        {
            try
            {
                if (SelectedFingerprint != null)
                {
                    Fingerprints.Remove(SelectedFingerprint);
                    StatusMessage = "设备指纹已删除";
                    _logService.Info($"删除设备指纹: {SelectedFingerprint.Id}");
                    SelectedFingerprint = null;
                }
            }
            catch (Exception ex)
            {
                _logService.Error("删除设备指纹失败", ex);
                StatusMessage = $"删除失败: {ex.Message}";
            }
            finally
            {
                UpdateCommandStates();
            }
        }

        /// <summary>
        /// 导出设备指纹
        /// </summary>
        private async Task ExportFingerprintAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "正在导出设备指纹...";

                var json = _deviceFingerprintService.ExportFingerprintToJson(SelectedFingerprint);
                var fileName = $"DeviceFingerprint_{SelectedFingerprint.Id}_{DateTime.Now:yyyyMMdd_HHmmss}.json";
                var filePath = System.IO.Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), fileName);

                await _fileService.WriteTextFileAsync(filePath, json);

                StatusMessage = $"设备指纹已导出到: {filePath}";
                _logService.Info($"导出设备指纹成功: {filePath}");
            }
            catch (Exception ex)
            {
                _logService.Error("导出设备指纹失败", ex);
                StatusMessage = $"导出失败: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
                UpdateCommandStates();
            }
        }

        /// <summary>
        /// 导入设备指纹
        /// </summary>
        private async Task ImportFingerprintAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "正在导入设备指纹...";

                // 这里应该打开文件对话框，暂时使用固定路径演示
                var filePath = System.IO.Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "fingerprint.json");

                if (_fileService.FileExists(filePath))
                {
                    var json = await _fileService.ReadTextFileAsync(filePath);
                    var fingerprint = _deviceFingerprintService.ImportFingerprintFromJson(json);

                    Fingerprints.Add(fingerprint);
                    SelectedFingerprint = fingerprint;

                    StatusMessage = $"设备指纹导入成功: {fingerprint.FingerprintHash}";
                    _logService.Info($"导入设备指纹成功: {fingerprint.Id}");
                }
                else
                {
                    StatusMessage = "导入文件不存在";
                }
            }
            catch (Exception ex)
            {
                _logService.Error("导入设备指纹失败", ex);
                StatusMessage = $"导入失败: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
                UpdateCommandStates();
            }
        }

        /// <summary>
        /// 生成设备指纹报告
        /// </summary>
        private async Task GenerateReportAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "正在生成设备指纹报告...";

                FingerprintReport = await _deviceFingerprintService.GenerateFingerprintReportAsync(SelectedFingerprint);

                StatusMessage = "设备指纹报告生成成功";
                _logService.Info($"生成设备指纹报告成功: {SelectedFingerprint.Id}");
            }
            catch (Exception ex)
            {
                _logService.Error("生成设备指纹报告失败", ex);
                StatusMessage = $"生成报告失败: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
                UpdateCommandStates();
            }
        }

        /// <summary>
        /// 刷新可用硬件组件
        /// </summary>
        private async Task RefreshAvailableComponentsAsync()
        {
            try
            {
                AvailableComponents = await _deviceFingerprintService.GetAvailableHardwareComponentsAsync();
                _logService.Debug($"刷新可用硬件组件成功，共 {AvailableComponents.Count} 个组件");
            }
            catch (Exception ex)
            {
                _logService.Error("刷新可用硬件组件失败", ex);
                AvailableComponents = new List<string>();
            }
        }

        /// <summary>
        /// 清空指纹列表
        /// </summary>
        private void ClearFingerprintList()
        {
            try
            {
                Fingerprints.Clear();
                SelectedFingerprint = null;
                CurrentDeviceFingerprint = null;
                FingerprintReport = string.Empty;
                StatusMessage = "指纹列表已清空";
                _logService.Info("清空设备指纹列表");
            }
            catch (Exception ex)
            {
                _logService.Error("清空指纹列表失败", ex);
                StatusMessage = $"清空失败: {ex.Message}";
            }
            finally
            {
                UpdateCommandStates();
            }
        }

        /// <summary>
        /// 更新命令状态
        /// </summary>
        private void UpdateCommandStates()
        {
            (GenerateCurrentFingerprintCommand as DelegateCommand)?.RaiseCanExecuteChanged();
            (GenerateCustomFingerprintCommand as DelegateCommand)?.RaiseCanExecuteChanged();
            (ValidateFingerprintCommand as DelegateCommand)?.RaiseCanExecuteChanged();
            (DeleteFingerprintCommand as DelegateCommand)?.RaiseCanExecuteChanged();
            (ExportFingerprintCommand as DelegateCommand)?.RaiseCanExecuteChanged();
            (ImportFingerprintCommand as DelegateCommand)?.RaiseCanExecuteChanged();
            (GenerateReportCommand as DelegateCommand)?.RaiseCanExecuteChanged();
            (RefreshComponentsCommand as DelegateCommand)?.RaiseCanExecuteChanged();
            (ClearListCommand as DelegateCommand)?.RaiseCanExecuteChanged();
        }
    }
}
