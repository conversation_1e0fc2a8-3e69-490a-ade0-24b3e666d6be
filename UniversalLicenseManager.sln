Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "UniversalLicenseManager.Shell", "src\UniversalLicenseManager.Shell\UniversalLicenseManager.Shell.csproj", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "UniversalLicenseManager.Core", "src\UniversalLicenseManager.Core\UniversalLicenseManager.Core.csproj", "{E7DBC1EB-3CF4-4508-B55B-8A80575259C9}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "UniversalLicenseManager.DeviceFingerprint", "src\UniversalLicenseManager.DeviceFingerprint\UniversalLicenseManager.DeviceFingerprint.csproj", "{37E36C25-C330-4594-B676-3221399A9078}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "UniversalLicenseManager.LicenseGeneration", "src\UniversalLicenseManager.LicenseGeneration\UniversalLicenseManager.LicenseGeneration.csproj", "{F05662E1-6D99-4AFF-9B7F-F6860BB5005F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "UniversalLicenseManager.Configuration", "src\UniversalLicenseManager.Configuration\UniversalLicenseManager.Configuration.csproj", "{75EF8BA9-3802-47C8-9928-198E50DE8ADE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "UniversalLicenseManager.DeveloperTools", "src\UniversalLicenseManager.DeveloperTools\UniversalLicenseManager.DeveloperTools.csproj", "{009E8DDB-2B7E-4C81-8945-4E64C7AD744F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "UniversalLicenseManager.SDK", "src\UniversalLicenseManager.SDK\UniversalLicenseManager.SDK.csproj", "{7652FDBC-B0E9-4862-85F9-79BB15B4F20F}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Tests", "Tests", "{79015E55-9023-4672-8541-8E4517D8C29C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Docs", "Docs", "{8C8C6C2E-EF07-4AAE-9060-3CA3D1DD4949}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.Build.0 = Release|Any CPU
		{E7DBC1EB-3CF4-4508-B55B-8A80575259C9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E7DBC1EB-3CF4-4508-B55B-8A80575259C9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E7DBC1EB-3CF4-4508-B55B-8A80575259C9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E7DBC1EB-3CF4-4508-B55B-8A80575259C9}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {12345678-1234-1234-1234-123456789012}
	EndGlobalSection
EndGlobal
