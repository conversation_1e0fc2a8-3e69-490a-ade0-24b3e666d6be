{"format": 1, "restore": {"D:\\00 Crack\\src\\UniversalLicenseManager.Shell\\UniversalLicenseManager.Shell.csproj": {}}, "projects": {"D:\\00 Crack\\src\\UniversalLicenseManager.Core\\UniversalLicenseManager.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\00 Crack\\src\\UniversalLicenseManager.Core\\UniversalLicenseManager.Core.csproj", "projectName": "UniversalLicenseManager.Core", "projectPath": "D:\\00 Crack\\src\\UniversalLicenseManager.Core\\UniversalLicenseManager.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\00 Crack\\src\\UniversalLicenseManager.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\DevExpress 23.2\\Components\\Offline Packages", "e:\\DevExpress 24.2\\Components\\Offline Packages", "E:\\Microsoft Visual Studio\\Shared\\NuGetPackages", "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Syncfusion Toolbox for WPF.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Prism.Core": {"target": "Package", "version": "[8.1.97, )"}, "System.Management": {"target": "Package", "version": "[7.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}, "D:\\00 Crack\\src\\UniversalLicenseManager.Shell\\UniversalLicenseManager.Shell.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\00 Crack\\src\\UniversalLicenseManager.Shell\\UniversalLicenseManager.Shell.csproj", "projectName": "UniversalLicenseManager.Shell", "projectPath": "D:\\00 Crack\\src\\UniversalLicenseManager.Shell\\UniversalLicenseManager.Shell.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\00 Crack\\src\\UniversalLicenseManager.Shell\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\DevExpress 23.2\\Components\\Offline Packages", "e:\\DevExpress 24.2\\Components\\Offline Packages", "E:\\Microsoft Visual Studio\\Shared\\NuGetPackages", "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Syncfusion Toolbox for WPF.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {"D:\\00 Crack\\src\\UniversalLicenseManager.Configuration\\UniversalLicenseManager.Configuration.csproj": {"projectPath": "D:\\00 Crack\\src\\UniversalLicenseManager.Configuration\\UniversalLicenseManager.Configuration.csproj"}, "D:\\00 Crack\\src\\UniversalLicenseManager.Core\\UniversalLicenseManager.Core.csproj": {"projectPath": "D:\\00 Crack\\src\\UniversalLicenseManager.Core\\UniversalLicenseManager.Core.csproj"}, "D:\\00 Crack\\src\\UniversalLicenseManager.DeveloperTools\\UniversalLicenseManager.DeveloperTools.csproj": {"projectPath": "D:\\00 Crack\\src\\UniversalLicenseManager.DeveloperTools\\UniversalLicenseManager.DeveloperTools.csproj"}, "D:\\00 Crack\\src\\UniversalLicenseManager.DeviceFingerprint\\UniversalLicenseManager.DeviceFingerprint.csproj": {"projectPath": "D:\\00 Crack\\src\\UniversalLicenseManager.DeviceFingerprint\\UniversalLicenseManager.DeviceFingerprint.csproj"}, "D:\\00 Crack\\src\\UniversalLicenseManager.LicenseGeneration\\UniversalLicenseManager.LicenseGeneration.csproj": {"projectPath": "D:\\00 Crack\\src\\UniversalLicenseManager.LicenseGeneration\\UniversalLicenseManager.LicenseGeneration.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"MaterialDesignColors": {"target": "Package", "version": "[2.1.4, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[4.9.0, )"}, "Prism.DryIoc": {"target": "Package", "version": "[8.1.97, )"}, "Prism.Wpf": {"target": "Package", "version": "[8.1.97, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}}}