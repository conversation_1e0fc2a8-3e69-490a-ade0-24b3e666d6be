using Prism.Ioc;
using Prism.Modularity;
using Prism.Regions;
using UniversalLicenseManager.Core.Services;
using UniversalLicenseManager.DeviceFingerprint.Services;
using UniversalLicenseManager.DeviceFingerprint.Views;

namespace UniversalLicenseManager.DeviceFingerprint
{
    /// <summary>
    /// 设备指纹生成与管理模块
    /// 提供设备硬件信息采集、指纹生成和管理功能
    /// </summary>
    public class DeviceFingerprintModule : IModule
    {
        private readonly IRegionManager _regionManager;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="regionManager">区域管理器</param>
        public DeviceFingerprintModule(IRegionManager regionManager)
        {
            _regionManager = regionManager;
        }

        /// <summary>
        /// 模块初始化
        /// </summary>
        /// <param name="containerProvider">容器提供者</param>
        public void OnInitialized(IContainerProvider containerProvider)
        {
            // 注册视图到区域导航
            _regionManager.RegisterViewWithRegion("MainContentRegion", typeof(DeviceFingerprintView));
        }

        /// <summary>
        /// 注册模块类型
        /// </summary>
        /// <param name="containerRegistry">容器注册器</param>
        public void RegisterTypes(IContainerRegistry containerRegistry)
        {
            // 注册设备指纹服务
            containerRegistry.RegisterSingleton<IDeviceFingerprintService, DeviceFingerprintService>();

            // 注册视图和ViewModel
            containerRegistry.RegisterForNavigation<DeviceFingerprintView>();

            // 注册其他相关服务
            containerRegistry.RegisterSingleton<IDeviceFingerprintRepository, DeviceFingerprintRepository>();
        }
    }
}
