using System;
using System.Security.Cryptography;
using System.Text;

namespace UniversalLicenseManager.Core.Services
{
    /// <summary>
    /// 加密服务实现
    /// 提供RSA、AES加密和哈希计算功能
    /// </summary>
    public class EncryptionService : IEncryptionService
    {
        /// <summary>
        /// 生成RSA密钥对
        /// </summary>
        /// <param name="keySize">密钥长度</param>
        /// <returns>公钥和私钥</returns>
        public (string PublicKey, string PrivateKey) GenerateRSAKeyPair(int keySize = 2048)
        {
            try
            {
                using (var rsa = RSA.Create(keySize))
                {
                    var publicKey = Convert.ToBase64String(rsa.ExportRSAPublicKey());
                    var privateKey = Convert.ToBase64String(rsa.ExportRSAPrivateKey());
                    return (publicKey, privateKey);
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"生成RSA密钥对失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// RSA加密
        /// </summary>
        /// <param name="plainText">明文</param>
        /// <param name="publicKey">公钥</param>
        /// <returns>加密后的Base64字符串</returns>
        public string RSAEncrypt(string plainText, string publicKey)
        {
            try
            {
                using (var rsa = RSA.Create())
                {
                    rsa.ImportRSAPublicKey(Convert.FromBase64String(publicKey), out _);
                    var data = Encoding.UTF8.GetBytes(plainText);
                    var encryptedData = rsa.Encrypt(data, RSAEncryptionPadding.OaepSHA256);
                    return Convert.ToBase64String(encryptedData);
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"RSA加密失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// RSA解密
        /// </summary>
        /// <param name="cipherText">密文</param>
        /// <param name="privateKey">私钥</param>
        /// <returns>解密后的明文</returns>
        public string RSADecrypt(string cipherText, string privateKey)
        {
            try
            {
                using (var rsa = RSA.Create())
                {
                    rsa.ImportRSAPrivateKey(Convert.FromBase64String(privateKey), out _);
                    var encryptedData = Convert.FromBase64String(cipherText);
                    var decryptedData = rsa.Decrypt(encryptedData, RSAEncryptionPadding.OaepSHA256);
                    return Encoding.UTF8.GetString(decryptedData);
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"RSA解密失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// AES加密
        /// </summary>
        /// <param name="plainText">明文</param>
        /// <param name="key">密钥</param>
        /// <param name="iv">初始化向量</param>
        /// <returns>加密后的Base64字符串</returns>
        public string AESEncrypt(string plainText, string key, string iv = null)
        {
            try
            {
                using (var aes = Aes.Create())
                {
                    aes.Key = Encoding.UTF8.GetBytes(key.PadRight(32).Substring(0, 32));
                    
                    if (!string.IsNullOrEmpty(iv))
                    {
                        aes.IV = Encoding.UTF8.GetBytes(iv.PadRight(16).Substring(0, 16));
                    }

                    using (var encryptor = aes.CreateEncryptor())
                    {
                        var data = Encoding.UTF8.GetBytes(plainText);
                        var encryptedData = encryptor.TransformFinalBlock(data, 0, data.Length);
                        
                        // 将IV和加密数据组合
                        var result = new byte[aes.IV.Length + encryptedData.Length];
                        Array.Copy(aes.IV, 0, result, 0, aes.IV.Length);
                        Array.Copy(encryptedData, 0, result, aes.IV.Length, encryptedData.Length);
                        
                        return Convert.ToBase64String(result);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"AES加密失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// AES解密
        /// </summary>
        /// <param name="cipherText">密文</param>
        /// <param name="key">密钥</param>
        /// <param name="iv">初始化向量</param>
        /// <returns>解密后的明文</returns>
        public string AESDecrypt(string cipherText, string key, string iv = null)
        {
            try
            {
                var fullCipher = Convert.FromBase64String(cipherText);
                
                using (var aes = Aes.Create())
                {
                    aes.Key = Encoding.UTF8.GetBytes(key.PadRight(32).Substring(0, 32));
                    
                    // 提取IV和加密数据
                    var ivBytes = new byte[16];
                    var encryptedData = new byte[fullCipher.Length - 16];
                    Array.Copy(fullCipher, 0, ivBytes, 0, 16);
                    Array.Copy(fullCipher, 16, encryptedData, 0, encryptedData.Length);
                    
                    aes.IV = ivBytes;

                    using (var decryptor = aes.CreateDecryptor())
                    {
                        var decryptedData = decryptor.TransformFinalBlock(encryptedData, 0, encryptedData.Length);
                        return Encoding.UTF8.GetString(decryptedData);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"AES解密失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 计算哈希值
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <param name="algorithm">哈希算法</param>
        /// <returns>哈希值</returns>
        public string ComputeHash(string input, string algorithm = "SHA256")
        {
            try
            {
                var data = Encoding.UTF8.GetBytes(input);
                byte[] hash;

                switch (algorithm.ToUpper())
                {
                    case "MD5":
                        using (var md5 = MD5.Create())
                            hash = md5.ComputeHash(data);
                        break;
                    case "SHA1":
                        using (var sha1 = SHA1.Create())
                            hash = sha1.ComputeHash(data);
                        break;
                    case "SHA256":
                        using (var sha256 = SHA256.Create())
                            hash = sha256.ComputeHash(data);
                        break;
                    case "SHA512":
                        using (var sha512 = SHA512.Create())
                            hash = sha512.ComputeHash(data);
                        break;
                    default:
                        throw new ArgumentException($"不支持的哈希算法: {algorithm}");
                }

                return BitConverter.ToString(hash).Replace("-", "").ToLower();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"计算哈希值失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 生成随机密钥
        /// </summary>
        /// <param name="length">密钥长度</param>
        /// <returns>随机密钥</returns>
        public string GenerateRandomKey(int length = 32)
        {
            try
            {
                using (var rng = RandomNumberGenerator.Create())
                {
                    var bytes = new byte[length];
                    rng.GetBytes(bytes);
                    return Convert.ToBase64String(bytes).Substring(0, length);
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"生成随机密钥失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 验证哈希值
        /// </summary>
        /// <param name="input">原始输入</param>
        /// <param name="hash">要验证的哈希值</param>
        /// <param name="algorithm">哈希算法</param>
        /// <returns>是否匹配</returns>
        public bool VerifyHash(string input, string hash, string algorithm = "SHA256")
        {
            try
            {
                var computedHash = ComputeHash(input, algorithm);
                return string.Equals(computedHash, hash, StringComparison.OrdinalIgnoreCase);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 数字签名
        /// </summary>
        /// <param name="data">要签名的数据</param>
        /// <param name="privateKey">私钥</param>
        /// <returns>数字签名</returns>
        public string SignData(string data, string privateKey)
        {
            try
            {
                using (var rsa = RSA.Create())
                {
                    rsa.ImportRSAPrivateKey(Convert.FromBase64String(privateKey), out _);
                    var dataBytes = Encoding.UTF8.GetBytes(data);
                    var signature = rsa.SignData(dataBytes, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);
                    return Convert.ToBase64String(signature);
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"数字签名失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 验证数字签名
        /// </summary>
        /// <param name="data">原始数据</param>
        /// <param name="signature">数字签名</param>
        /// <param name="publicKey">公钥</param>
        /// <returns>签名是否有效</returns>
        public bool VerifySignature(string data, string signature, string publicKey)
        {
            try
            {
                using (var rsa = RSA.Create())
                {
                    rsa.ImportRSAPublicKey(Convert.FromBase64String(publicKey), out _);
                    var dataBytes = Encoding.UTF8.GetBytes(data);
                    var signatureBytes = Convert.FromBase64String(signature);
                    return rsa.VerifyData(dataBytes, signatureBytes, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);
                }
            }
            catch
            {
                return false;
            }
        }
    }
}
