using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;

namespace UniversalLicenseManager.Core.Models
{
    /// <summary>
    /// 授权配置数据模型
    /// 定义产品的授权类型和功能模块配置
    /// </summary>
    public class AuthorizationConfig
    {
        /// <summary>
        /// 配置唯一标识符
        /// </summary>
        [JsonProperty("configId")]
        public string ConfigId { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 产品名称
        /// </summary>
        [JsonProperty("productName")]
        public string ProductName { get; set; }

        /// <summary>
        /// 产品版本
        /// </summary>
        [JsonProperty("productVersion")]
        public string ProductVersion { get; set; }

        /// <summary>
        /// 产品描述
        /// </summary>
        [JsonProperty("productDescription")]
        public string ProductDescription { get; set; }

        /// <summary>
        /// 授权类型列表
        /// </summary>
        [JsonProperty("licenseTypes")]
        public List<LicenseType> LicenseTypes { get; set; } = new List<LicenseType>();

        /// <summary>
        /// 可用功能模块列表
        /// </summary>
        [JsonProperty("availableFeatures")]
        public List<FeatureModule> AvailableFeatures { get; set; } = new List<FeatureModule>();

        /// <summary>
        /// 配置创建时间
        /// </summary>
        [JsonProperty("createdAt")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 配置最后更新时间
        /// </summary>
        [JsonProperty("updatedAt")]
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 配置创建者
        /// </summary>
        [JsonProperty("createdBy")]
        public string CreatedBy { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        [JsonProperty("isActive")]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 备注信息
        /// </summary>
        [JsonProperty("remarks")]
        public string Remarks { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public AuthorizationConfig()
        {
        }

        /// <summary>
        /// 带参数的构造函数
        /// </summary>
        /// <param name="productName">产品名称</param>
        public AuthorizationConfig(string productName)
        {
            ProductName = productName;
        }

        /// <summary>
        /// 添加授权类型
        /// </summary>
        /// <param name="licenseType">授权类型</param>
        public void AddLicenseType(LicenseType licenseType)
        {
            if (licenseType != null && !LicenseTypes.Any(lt => lt.TypeName == licenseType.TypeName))
            {
                LicenseTypes.Add(licenseType);
                UpdatedAt = DateTime.Now;
            }
        }

        /// <summary>
        /// 移除授权类型
        /// </summary>
        /// <param name="typeName">授权类型名称</param>
        /// <returns>是否成功移除</returns>
        public bool RemoveLicenseType(string typeName)
        {
            var licenseType = LicenseTypes.FirstOrDefault(lt => lt.TypeName == typeName);
            if (licenseType != null)
            {
                LicenseTypes.Remove(licenseType);
                UpdatedAt = DateTime.Now;
                return true;
            }
            return false;
        }

        /// <summary>
        /// 添加功能模块
        /// </summary>
        /// <param name="feature">功能模块</param>
        public void AddFeature(FeatureModule feature)
        {
            if (feature != null && !AvailableFeatures.Any(f => f.FeatureId == feature.FeatureId))
            {
                AvailableFeatures.Add(feature);
                UpdatedAt = DateTime.Now;
            }
        }

        /// <summary>
        /// 移除功能模块
        /// </summary>
        /// <param name="featureId">功能模块ID</param>
        /// <returns>是否成功移除</returns>
        public bool RemoveFeature(string featureId)
        {
            var feature = AvailableFeatures.FirstOrDefault(f => f.FeatureId == featureId);
            if (feature != null)
            {
                AvailableFeatures.Remove(feature);
                UpdatedAt = DateTime.Now;
                return true;
            }
            return false;
        }

        /// <summary>
        /// 获取指定授权类型
        /// </summary>
        /// <param name="typeName">授权类型名称</param>
        /// <returns>授权类型</returns>
        public LicenseType GetLicenseType(string typeName)
        {
            return LicenseTypes.FirstOrDefault(lt => lt.TypeName == typeName);
        }

        /// <summary>
        /// 获取指定功能模块
        /// </summary>
        /// <param name="featureId">功能模块ID</param>
        /// <returns>功能模块</returns>
        public FeatureModule GetFeature(string featureId)
        {
            return AvailableFeatures.FirstOrDefault(f => f.FeatureId == featureId);
        }

        /// <summary>
        /// 验证配置是否有效
        /// </summary>
        /// <returns>是否有效</returns>
        public bool IsValid()
        {
            return !string.IsNullOrEmpty(ProductName) && 
                   LicenseTypes.Any() && 
                   AvailableFeatures.Any();
        }

        /// <summary>
        /// 重写ToString方法
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return $"AuthorizationConfig: {ProductName} v{ProductVersion} ({LicenseTypes.Count} types, {AvailableFeatures.Count} features)";
        }
    }

    /// <summary>
    /// 授权类型
    /// </summary>
    public class LicenseType
    {
        /// <summary>
        /// 类型名称（唯一标识）
        /// </summary>
        [JsonProperty("typeName")]
        public string TypeName { get; set; }

        /// <summary>
        /// 显示名称
        /// </summary>
        [JsonProperty("displayName")]
        public string DisplayName { get; set; }

        /// <summary>
        /// 类型描述
        /// </summary>
        [JsonProperty("description")]
        public string Description { get; set; }

        /// <summary>
        /// 包含的功能模块ID列表
        /// </summary>
        [JsonProperty("features")]
        public List<string> Features { get; set; } = new List<string>();

        /// <summary>
        /// 授权持续时间（天数，null表示永久）
        /// </summary>
        [JsonProperty("duration")]
        public int? Duration { get; set; }

        /// <summary>
        /// 最大设备数量限制
        /// </summary>
        [JsonProperty("maxDevices")]
        public int? MaxDevices { get; set; }

        /// <summary>
        /// 价格（可选）
        /// </summary>
        [JsonProperty("price")]
        public decimal? Price { get; set; }

        /// <summary>
        /// 货币单位
        /// </summary>
        [JsonProperty("currency")]
        public string Currency { get; set; } = "CNY";

        /// <summary>
        /// 是否激活
        /// </summary>
        [JsonProperty("isActive")]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 排序顺序
        /// </summary>
        [JsonProperty("sortOrder")]
        public int SortOrder { get; set; }

        /// <summary>
        /// 检查是否包含指定功能
        /// </summary>
        /// <param name="featureId">功能ID</param>
        /// <returns>是否包含</returns>
        public bool HasFeature(string featureId)
        {
            return Features?.Contains(featureId) == true;
        }

        /// <summary>
        /// 添加功能
        /// </summary>
        /// <param name="featureId">功能ID</param>
        public void AddFeature(string featureId)
        {
            if (!string.IsNullOrEmpty(featureId) && !HasFeature(featureId))
            {
                Features.Add(featureId);
            }
        }

        /// <summary>
        /// 移除功能
        /// </summary>
        /// <param name="featureId">功能ID</param>
        /// <returns>是否成功移除</returns>
        public bool RemoveFeature(string featureId)
        {
            return Features?.Remove(featureId) == true;
        }
    }

    /// <summary>
    /// 功能模块
    /// </summary>
    public class FeatureModule
    {
        /// <summary>
        /// 功能模块唯一标识符
        /// </summary>
        [JsonProperty("featureId")]
        public string FeatureId { get; set; }

        /// <summary>
        /// 功能名称
        /// </summary>
        [JsonProperty("featureName")]
        public string FeatureName { get; set; }

        /// <summary>
        /// 功能描述
        /// </summary>
        [JsonProperty("description")]
        public string Description { get; set; }

        /// <summary>
        /// 功能分类
        /// </summary>
        [JsonProperty("category")]
        public string Category { get; set; }

        /// <summary>
        /// 是否为必需功能
        /// </summary>
        [JsonProperty("isRequired")]
        public bool IsRequired { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        [JsonProperty("isActive")]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 排序顺序
        /// </summary>
        [JsonProperty("sortOrder")]
        public int SortOrder { get; set; }

        /// <summary>
        /// 依赖的功能模块ID列表
        /// </summary>
        [JsonProperty("dependencies")]
        public List<string> Dependencies { get; set; } = new List<string>();

        /// <summary>
        /// 功能版本
        /// </summary>
        [JsonProperty("version")]
        public string Version { get; set; } = "1.0.0";

        /// <summary>
        /// 检查是否依赖指定功能
        /// </summary>
        /// <param name="featureId">功能ID</param>
        /// <returns>是否依赖</returns>
        public bool DependsOn(string featureId)
        {
            return Dependencies?.Contains(featureId) == true;
        }

        /// <summary>
        /// 添加依赖
        /// </summary>
        /// <param name="featureId">功能ID</param>
        public void AddDependency(string featureId)
        {
            if (!string.IsNullOrEmpty(featureId) && !DependsOn(featureId))
            {
                Dependencies.Add(featureId);
            }
        }

        /// <summary>
        /// 移除依赖
        /// </summary>
        /// <param name="featureId">功能ID</param>
        /// <returns>是否成功移除</returns>
        public bool RemoveDependency(string featureId)
        {
            return Dependencies?.Remove(featureId) == true;
        }
    }
}
