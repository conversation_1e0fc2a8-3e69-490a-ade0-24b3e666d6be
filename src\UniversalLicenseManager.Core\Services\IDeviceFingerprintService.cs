using System.Collections.Generic;
using System.Threading.Tasks;
using UniversalLicenseManager.Core.Models;

namespace UniversalLicenseManager.Core.Services
{
    /// <summary>
    /// 设备指纹生成策略
    /// </summary>
    public enum FingerprintStrategy
    {
        /// <summary>
        /// 仅CPU信息
        /// </summary>
        CpuOnly,

        /// <summary>
        /// 仅主板信息
        /// </summary>
        MotherboardOnly,

        /// <summary>
        /// 仅硬盘信息
        /// </summary>
        HardDiskOnly,

        /// <summary>
        /// 仅MAC地址
        /// </summary>
        MacAddressOnly,

        /// <summary>
        /// CPU + 主板
        /// </summary>
        CpuAndMotherboard,

        /// <summary>
        /// CPU + 硬盘
        /// </summary>
        CpuAndHardDisk,

        /// <summary>
        /// 主板 + 硬盘
        /// </summary>
        MotherboardAndHardDisk,

        /// <summary>
        /// 所有硬件信息组合
        /// </summary>
        AllHardware,

        /// <summary>
        /// 自定义组合
        /// </summary>
        Custom
    }

    /// <summary>
    /// 设备指纹服务接口
    /// 提供设备硬件信息采集和指纹生成功能
    /// </summary>
    public interface IDeviceFingerprintService
    {
        /// <summary>
        /// 获取CPU序列号
        /// </summary>
        /// <returns>CPU序列号</returns>
        Task<string> GetCpuIdAsync();

        /// <summary>
        /// 获取主板序列号
        /// </summary>
        /// <returns>主板序列号</returns>
        Task<string> GetMotherboardIdAsync();

        /// <summary>
        /// 获取硬盘序列号
        /// </summary>
        /// <returns>硬盘序列号</returns>
        Task<string> GetHardDiskIdAsync();

        /// <summary>
        /// 获取MAC地址
        /// </summary>
        /// <returns>MAC地址</returns>
        Task<string> GetMacAddressAsync();

        /// <summary>
        /// 获取系统UUID
        /// </summary>
        /// <returns>系统UUID</returns>
        Task<string> GetSystemUuidAsync();

        /// <summary>
        /// 获取设备名称
        /// </summary>
        /// <returns>设备名称</returns>
        string GetDeviceName();

        /// <summary>
        /// 获取所有硬件信息
        /// </summary>
        /// <returns>硬件信息字典</returns>
        Task<Dictionary<string, string>> GetAllHardwareInfoAsync();

        /// <summary>
        /// 生成设备指纹
        /// </summary>
        /// <param name="strategy">生成策略</param>
        /// <param name="algorithm">哈希算法</param>
        /// <returns>设备指纹对象</returns>
        Task<DeviceFingerprint> GenerateFingerprintAsync(FingerprintStrategy strategy = FingerprintStrategy.AllHardware, string algorithm = "SHA256");

        /// <summary>
        /// 使用自定义组合生成设备指纹
        /// </summary>
        /// <param name="includeComponents">要包含的硬件组件</param>
        /// <param name="algorithm">哈希算法</param>
        /// <returns>设备指纹对象</returns>
        Task<DeviceFingerprint> GenerateCustomFingerprintAsync(List<string> includeComponents, string algorithm = "SHA256");

        /// <summary>
        /// 验证设备指纹
        /// </summary>
        /// <param name="fingerprint">要验证的指纹</param>
        /// <param name="strategy">验证策略</param>
        /// <returns>是否匹配</returns>
        Task<bool> ValidateFingerprintAsync(DeviceFingerprint fingerprint, FingerprintStrategy strategy = FingerprintStrategy.AllHardware);

        /// <summary>
        /// 比较两个设备指纹
        /// </summary>
        /// <param name="fingerprint1">指纹1</param>
        /// <param name="fingerprint2">指纹2</param>
        /// <returns>相似度（0-1之间）</returns>
        double CompareFingerprintSimilarity(DeviceFingerprint fingerprint1, DeviceFingerprint fingerprint2);

        /// <summary>
        /// 获取当前设备的指纹字符串
        /// </summary>
        /// <param name="strategy">生成策略</param>
        /// <returns>指纹字符串</returns>
        Task<string> GetCurrentDeviceFingerprintStringAsync(FingerprintStrategy strategy = FingerprintStrategy.AllHardware);

        /// <summary>
        /// 检查硬件组件是否可用
        /// </summary>
        /// <param name="component">硬件组件名称</param>
        /// <returns>是否可用</returns>
        Task<bool> IsHardwareComponentAvailableAsync(string component);

        /// <summary>
        /// 获取可用的硬件组件列表
        /// </summary>
        /// <returns>硬件组件列表</returns>
        Task<List<string>> GetAvailableHardwareComponentsAsync();

        /// <summary>
        /// 导出设备指纹为JSON
        /// </summary>
        /// <param name="fingerprint">设备指纹</param>
        /// <returns>JSON字符串</returns>
        string ExportFingerprintToJson(DeviceFingerprint fingerprint);

        /// <summary>
        /// 从JSON导入设备指纹
        /// </summary>
        /// <param name="json">JSON字符串</param>
        /// <returns>设备指纹对象</returns>
        DeviceFingerprint ImportFingerprintFromJson(string json);

        /// <summary>
        /// 生成设备指纹报告
        /// </summary>
        /// <param name="fingerprint">设备指纹</param>
        /// <returns>详细报告</returns>
        Task<string> GenerateFingerprintReportAsync(DeviceFingerprint fingerprint);
    }
}
