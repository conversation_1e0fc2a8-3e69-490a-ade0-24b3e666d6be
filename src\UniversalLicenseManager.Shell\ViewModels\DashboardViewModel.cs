using Prism.Commands;
using Prism.Mvvm;
using Prism.Regions;
using System.Windows.Input;

namespace UniversalLicenseManager.Shell.ViewModels
{
    /// <summary>
    /// 仪表板ViewModel
    /// 负责仪表板页面的业务逻辑和数据绑定
    /// </summary>
    public class DashboardViewModel : BindableBase
    {
        private readonly IRegionManager _regionManager;

        #region 属性

        private int _deviceFingerprintCount = 0;
        /// <summary>
        /// 设备指纹数量
        /// </summary>
        public int DeviceFingerprintCount
        {
            get { return _deviceFingerprintCount; }
            set { SetProperty(ref _deviceFingerprintCount, value); }
        }

        private int _licenseCount = 0;
        /// <summary>
        /// License总数量
        /// </summary>
        public int LicenseCount
        {
            get { return _licenseCount; }
            set { SetProperty(ref _licenseCount, value); }
        }

        private int _configurationCount = 0;
        /// <summary>
        /// 配置模板数量
        /// </summary>
        public int ConfigurationCount
        {
            get { return _configurationCount; }
            set { SetProperty(ref _configurationCount, value); }
        }

        private int _activeLicenseCount = 0;
        /// <summary>
        /// 活跃License数量
        /// </summary>
        public int ActiveLicenseCount
        {
            get { return _activeLicenseCount; }
            set { SetProperty(ref _activeLicenseCount, value); }
        }

        #endregion

        #region 命令

        /// <summary>
        /// 导航到设备指纹管理页面命令
        /// </summary>
        public ICommand NavigateToDeviceFingerprintCommand { get; private set; }

        /// <summary>
        /// 导航到License生成页面命令
        /// </summary>
        public ICommand NavigateToLicenseGenerationCommand { get; private set; }

        /// <summary>
        /// 导航到配置管理页面命令
        /// </summary>
        public ICommand NavigateToConfigurationCommand { get; private set; }

        /// <summary>
        /// 导航到开发者工具页面命令
        /// </summary>
        public ICommand NavigateToDeveloperToolsCommand { get; private set; }

        #endregion

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="regionManager">区域管理器</param>
        public DashboardViewModel(IRegionManager regionManager)
        {
            _regionManager = regionManager;
            
            InitializeCommands();
            LoadDashboardData();
        }

        /// <summary>
        /// 初始化命令
        /// </summary>
        private void InitializeCommands()
        {
            NavigateToDeviceFingerprintCommand = new DelegateCommand(NavigateToDeviceFingerprint);
            NavigateToLicenseGenerationCommand = new DelegateCommand(NavigateToLicenseGeneration);
            NavigateToConfigurationCommand = new DelegateCommand(NavigateToConfiguration);
            NavigateToDeveloperToolsCommand = new DelegateCommand(NavigateToDeveloperTools);
        }

        /// <summary>
        /// 加载仪表板数据
        /// </summary>
        private void LoadDashboardData()
        {
            // TODO: 从服务中加载实际数据
            // 这里先使用模拟数据
            DeviceFingerprintCount = 0;
            LicenseCount = 0;
            ConfigurationCount = 0;
            ActiveLicenseCount = 0;
        }

        #region 导航方法

        /// <summary>
        /// 导航到设备指纹管理页面
        /// </summary>
        private void NavigateToDeviceFingerprint()
        {
            _regionManager.RequestNavigate("MainContentRegion", "DeviceFingerprintView");
        }

        /// <summary>
        /// 导航到License生成页面
        /// </summary>
        private void NavigateToLicenseGeneration()
        {
            _regionManager.RequestNavigate("MainContentRegion", "LicenseGenerationView");
        }

        /// <summary>
        /// 导航到配置管理页面
        /// </summary>
        private void NavigateToConfiguration()
        {
            _regionManager.RequestNavigate("MainContentRegion", "ConfigurationView");
        }

        /// <summary>
        /// 导航到开发者工具页面
        /// </summary>
        private void NavigateToDeveloperTools()
        {
            _regionManager.RequestNavigate("MainContentRegion", "DeveloperToolsView");
        }

        #endregion

        /// <summary>
        /// 刷新仪表板数据
        /// </summary>
        public void RefreshData()
        {
            LoadDashboardData();
        }
    }
}
