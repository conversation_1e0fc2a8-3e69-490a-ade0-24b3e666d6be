{"Version": 1, "WorkspaceRootPath": "D:\\00 Crack\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{E7DBC1EB-3CF4-4508-B55B-8A80575259C9}|src\\UniversalLicenseManager.Core\\UniversalLicenseManager.Core.csproj|d:\\00 crack\\src\\universallicensemanager.core\\utilities\\jsonhelper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E7DBC1EB-3CF4-4508-B55B-8A80575259C9}|src\\UniversalLicenseManager.Core\\UniversalLicenseManager.Core.csproj|solutionrelative:src\\universallicensemanager.core\\utilities\\jsonhelper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|src\\UniversalLicenseManager.Shell\\UniversalLicenseManager.Shell.csproj|d:\\00 crack\\src\\universallicensemanager.shell\\views\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|src\\UniversalLicenseManager.Shell\\UniversalLicenseManager.Shell.csproj|solutionrelative:src\\universallicensemanager.shell\\views\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|src\\UniversalLicenseManager.Shell\\UniversalLicenseManager.Shell.csproj|d:\\00 crack\\src\\universallicensemanager.shell\\app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|src\\UniversalLicenseManager.Shell\\UniversalLicenseManager.Shell.csproj|solutionrelative:src\\universallicensemanager.shell\\app.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|src\\UniversalLicenseManager.Shell\\UniversalLicenseManager.Shell.csproj|d:\\00 crack\\src\\universallicensemanager.shell\\app.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|src\\UniversalLicenseManager.Shell\\UniversalLicenseManager.Shell.csproj|solutionrelative:src\\universallicensemanager.shell\\app.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{37E36C25-C330-4594-B676-3221399A9078}|src\\UniversalLicenseManager.DeviceFingerprint\\UniversalLicenseManager.DeviceFingerprint.csproj|d:\\00 crack\\src\\universallicensemanager.devicefingerprint\\views\\devicefingerprintview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{37E36C25-C330-4594-B676-3221399A9078}|src\\UniversalLicenseManager.DeviceFingerprint\\UniversalLicenseManager.DeviceFingerprint.csproj|solutionrelative:src\\universallicensemanager.devicefingerprint\\views\\devicefingerprintview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{37E36C25-C330-4594-B676-3221399A9078}|src\\UniversalLicenseManager.DeviceFingerprint\\UniversalLicenseManager.DeviceFingerprint.csproj|d:\\00 crack\\src\\universallicensemanager.devicefingerprint\\devicefingerprintmodule.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{37E36C25-C330-4594-B676-3221399A9078}|src\\UniversalLicenseManager.DeviceFingerprint\\UniversalLicenseManager.DeviceFingerprint.csproj|solutionrelative:src\\universallicensemanager.devicefingerprint\\devicefingerprintmodule.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E7DBC1EB-3CF4-4508-B55B-8A80575259C9}|src\\UniversalLicenseManager.Core\\UniversalLicenseManager.Core.csproj|d:\\00 crack\\src\\universallicensemanager.core\\models\\authorizationconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E7DBC1EB-3CF4-4508-B55B-8A80575259C9}|src\\UniversalLicenseManager.Core\\UniversalLicenseManager.Core.csproj|solutionrelative:src\\universallicensemanager.core\\models\\authorizationconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E7DBC1EB-3CF4-4508-B55B-8A80575259C9}|src\\UniversalLicenseManager.Core\\UniversalLicenseManager.Core.csproj|d:\\00 crack\\src\\universallicensemanager.core\\coremodule.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E7DBC1EB-3CF4-4508-B55B-8A80575259C9}|src\\UniversalLicenseManager.Core\\UniversalLicenseManager.Core.csproj|solutionrelative:src\\universallicensemanager.core\\coremodule.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|src\\UniversalLicenseManager.Shell\\UniversalLicenseManager.Shell.csproj|d:\\00 crack\\src\\universallicensemanager.shell\\views\\dashboardview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|src\\UniversalLicenseManager.Shell\\UniversalLicenseManager.Shell.csproj|solutionrelative:src\\universallicensemanager.shell\\views\\dashboardview.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|src\\UniversalLicenseManager.Shell\\UniversalLicenseManager.Shell.csproj|d:\\00 crack\\src\\universallicensemanager.shell\\viewmodels\\mainwindowviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|src\\UniversalLicenseManager.Shell\\UniversalLicenseManager.Shell.csproj|solutionrelative:src\\universallicensemanager.shell\\viewmodels\\mainwindowviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 9, "Children": [{"$type": "Document", "DocumentIndex": 2, "Title": "App.xaml", "DocumentMoniker": "D:\\00 Crack\\src\\UniversalLicenseManager.Shell\\App.xaml", "RelativeDocumentMoniker": "src\\UniversalLicenseManager.Shell\\App.xaml", "ToolTip": "D:\\00 Crack\\src\\UniversalLicenseManager.Shell\\App.xaml", "RelativeToolTip": "src\\UniversalLicenseManager.Shell\\App.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-11T05:29:30.149Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:130:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:132:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:131:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:133:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:134:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:135:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d84ee353-0bef-5a41-a649-8f89aca5d84d}"}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "JsonHelper.cs", "DocumentMoniker": "D:\\00 Crack\\src\\UniversalLicenseManager.Core\\Utilities\\JsonHelper.cs", "RelativeDocumentMoniker": "src\\UniversalLicenseManager.Core\\Utilities\\JsonHelper.cs", "ToolTip": "D:\\00 Crack\\src\\UniversalLicenseManager.Core\\Utilities\\JsonHelper.cs*", "RelativeToolTip": "src\\UniversalLicenseManager.Core\\Utilities\\JsonHelper.cs*", "ViewState": "AgIAADYBAAAAAAAAAAAUwEYBAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-11T05:28:01.122Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "DeviceFingerprintModule.cs", "DocumentMoniker": "D:\\00 Crack\\src\\UniversalLicenseManager.DeviceFingerprint\\DeviceFingerprintModule.cs", "RelativeDocumentMoniker": "src\\UniversalLicenseManager.DeviceFingerprint\\DeviceFingerprintModule.cs", "ToolTip": "D:\\00 Crack\\src\\UniversalLicenseManager.DeviceFingerprint\\DeviceFingerprintModule.cs", "RelativeToolTip": "src\\UniversalLicenseManager.DeviceFingerprint\\DeviceFingerprintModule.cs", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-11T05:26:45.249Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "DeviceFingerprintView.xaml", "DocumentMoniker": "D:\\00 Crack\\src\\UniversalLicenseManager.DeviceFingerprint\\Views\\DeviceFingerprintView.xaml", "RelativeDocumentMoniker": "src\\UniversalLicenseManager.DeviceFingerprint\\Views\\DeviceFingerprintView.xaml", "ToolTip": "D:\\00 Crack\\src\\UniversalLicenseManager.DeviceFingerprint\\Views\\DeviceFingerprintView.xaml", "RelativeToolTip": "src\\UniversalLicenseManager.DeviceFingerprint\\Views\\DeviceFingerprintView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-11T05:26:39.979Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "AuthorizationConfig.cs", "DocumentMoniker": "D:\\00 Crack\\src\\UniversalLicenseManager.Core\\Models\\AuthorizationConfig.cs", "RelativeDocumentMoniker": "src\\UniversalLicenseManager.Core\\Models\\AuthorizationConfig.cs", "ToolTip": "D:\\00 Crack\\src\\UniversalLicenseManager.Core\\Models\\AuthorizationConfig.cs", "RelativeToolTip": "src\\UniversalLicenseManager.Core\\Models\\AuthorizationConfig.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-11T03:43:43.287Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "MainWindow.xaml", "DocumentMoniker": "D:\\00 Crack\\src\\UniversalLicenseManager.Shell\\Views\\MainWindow.xaml", "RelativeDocumentMoniker": "src\\UniversalLicenseManager.Shell\\Views\\MainWindow.xaml", "ToolTip": "D:\\00 Crack\\src\\UniversalLicenseManager.Shell\\Views\\MainWindow.xaml*", "RelativeToolTip": "src\\UniversalLicenseManager.Shell\\Views\\MainWindow.xaml*", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-11T03:35:06.768Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "App.xaml.cs", "DocumentMoniker": "D:\\00 Crack\\src\\UniversalLicenseManager.Shell\\App.xaml.cs", "RelativeDocumentMoniker": "src\\UniversalLicenseManager.Shell\\App.xaml.cs", "ToolTip": "D:\\00 Crack\\src\\UniversalLicenseManager.Shell\\App.xaml.cs", "RelativeToolTip": "src\\UniversalLicenseManager.Shell\\App.xaml.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAGEAAABVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-11T03:32:22.801Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "CoreModule.cs", "DocumentMoniker": "D:\\00 Crack\\src\\UniversalLicenseManager.Core\\CoreModule.cs", "RelativeDocumentMoniker": "src\\UniversalLicenseManager.Core\\CoreModule.cs", "ToolTip": "D:\\00 Crack\\src\\UniversalLicenseManager.Core\\CoreModule.cs", "RelativeToolTip": "src\\UniversalLicenseManager.Core\\CoreModule.cs", "ViewState": "AgIAAAkAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-11T03:38:30.235Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "DashboardView.xaml", "DocumentMoniker": "D:\\00 Crack\\src\\UniversalLicenseManager.Shell\\Views\\DashboardView.xaml", "RelativeDocumentMoniker": "src\\UniversalLicenseManager.Shell\\Views\\DashboardView.xaml", "ToolTip": "D:\\00 Crack\\src\\UniversalLicenseManager.Shell\\Views\\DashboardView.xaml", "RelativeToolTip": "src\\UniversalLicenseManager.Shell\\Views\\DashboardView.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-06-11T03:35:48.718Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "MainWindowViewModel.cs", "DocumentMoniker": "D:\\00 Crack\\src\\UniversalLicenseManager.Shell\\ViewModels\\MainWindowViewModel.cs", "RelativeDocumentMoniker": "src\\UniversalLicenseManager.Shell\\ViewModels\\MainWindowViewModel.cs", "ToolTip": "D:\\00 Crack\\src\\UniversalLicenseManager.Shell\\ViewModels\\MainWindowViewModel.cs", "RelativeToolTip": "src\\UniversalLicenseManager.Shell\\ViewModels\\MainWindowViewModel.cs", "ViewState": "AgIAADwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-11T03:32:43.924Z", "EditorCaption": ""}]}]}]}