<Window
    x:Class="UniversalLicenseManager.Shell.Views.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:prism="http://prismlibrary.com/"
    prism:ViewModelLocator.AutoWireViewModel="True"
    Background="{DynamicResource MaterialDesignPaper}"
    FontFamily="{DynamicResource MaterialDesignFont}"
    Style="{StaticResource MainWindowStyle}"
    TextElement.FontSize="13"
    TextElement.FontWeight="Regular"
    TextElement.Foreground="{DynamicResource MaterialDesignBody}"
    TextOptions.TextFormattingMode="Ideal"
    TextOptions.TextRenderingMode="Auto">

    <WindowChrome.WindowChrome>
        <WindowChrome
            CaptionHeight="40"
            ResizeBorderThickness="5"
            UseAeroCaptionButtons="False" />
    </WindowChrome.WindowChrome>

    <Border
        Background="{DynamicResource MaterialDesignPaper}"
        BorderBrush="{DynamicResource PrimaryHueMidBrush}"
        BorderThickness="1"
        CornerRadius="8">
        <Grid>
            <Grid.RowDefinitions>
                <!--  标题栏  -->
                <RowDefinition Height="40" />
                <!--  主内容区域  -->
                <RowDefinition Height="*" />
                <!--  状态栏  -->
                <RowDefinition Height="30" />
            </Grid.RowDefinitions>

            <!--  自定义标题栏  -->
            <Grid Grid.Row="0" Style="{StaticResource TitleBarStyle}">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <!--  应用程序图标和标题  -->
                <StackPanel
                    Grid.Column="0"
                    Margin="10,0"
                    VerticalAlignment="Center"
                    Orientation="Horizontal">
                    <materialDesign:PackIcon
                        Width="20"
                        Height="20"
                        VerticalAlignment="Center"
                        Foreground="White"
                        Kind="Security" />
                    <TextBlock
                        Margin="8,0,0,0"
                        VerticalAlignment="Center"
                        FontWeight="Medium"
                        Foreground="White"
                        Text="Universal License Manager" />
                </StackPanel>

                <!--  窗口控制按钮  -->
                <StackPanel
                    Grid.Column="2"
                    Orientation="Horizontal"
                    WindowChrome.IsHitTestVisibleInChrome="True">
                    <Button
                        Name="MinimizeButton"
                        Width="40"
                        Height="40"
                        Click="MinimizeButton_Click"
                        Style="{StaticResource MaterialDesignFlatButton}">
                        <materialDesign:PackIcon Foreground="White" Kind="WindowMinimize" />
                    </Button>
                    <Button
                        Name="MaximizeButton"
                        Width="40"
                        Height="40"
                        Click="MaximizeButton_Click"
                        Style="{StaticResource MaterialDesignFlatButton}">
                        <materialDesign:PackIcon Foreground="White" Kind="WindowMaximize" />
                    </Button>
                    <Button
                        Name="CloseButton"
                        Width="40"
                        Height="40"
                        Click="CloseButton_Click"
                        Style="{StaticResource MaterialDesignFlatButton}">
                        <materialDesign:PackIcon Foreground="White" Kind="WindowClose" />
                    </Button>
                </StackPanel>
            </Grid>

            <!--  主内容区域  -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <!--  导航菜单  -->
                    <ColumnDefinition Width="280" />
                    <!--  内容区域  -->
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>

                <!--  左侧导航菜单  -->
                <Border
                    Grid.Column="0"
                    Background="{DynamicResource MaterialDesignCardBackground}"
                    BorderBrush="{DynamicResource MaterialDesignDivider}"
                    BorderThickness="0,0,1,0">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <ListBox
                            Name="NavigationMenu"
                            SelectionChanged="NavigationMenu_SelectionChanged"
                            Style="{StaticResource NavigationMenuStyle}">
                            <ListBoxItem>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon
                                        Width="20"
                                        Height="20"
                                        Margin="0,0,16,0"
                                        Kind="TabletDashboard" />
                                    <TextBlock VerticalAlignment="Center" Text="仪表板" />
                                </StackPanel>
                            </ListBoxItem>
                            <ListBoxItem>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon
                                        Width="20"
                                        Height="20"
                                        Margin="0,0,16,0"
                                        Kind="Fingerprint" />
                                    <TextBlock VerticalAlignment="Center" Text="设备指纹管理" />
                                </StackPanel>
                            </ListBoxItem>
                            <ListBoxItem>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon
                                        Width="20"
                                        Height="20"
                                        Margin="0,0,16,0"
                                        Kind="Key" />
                                    <TextBlock VerticalAlignment="Center" Text="License生成" />
                                </StackPanel>
                            </ListBoxItem>
                            <ListBoxItem>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon
                                        Width="20"
                                        Height="20"
                                        Margin="0,0,16,0"
                                        Kind="Settings" />
                                    <TextBlock VerticalAlignment="Center" Text="授权配置" />
                                </StackPanel>
                            </ListBoxItem>
                            <ListBoxItem>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon
                                        Width="20"
                                        Height="20"
                                        Margin="0,0,16,0"
                                        Kind="CodeBraces" />
                                    <TextBlock VerticalAlignment="Center" Text="开发者工具" />
                                </StackPanel>
                            </ListBoxItem>
                        </ListBox>
                    </ScrollViewer>
                </Border>

                <!--  右侧内容区域  -->
                <ContentControl
                    Name="MainContentRegion"
                    Grid.Column="1"
                    prism:RegionManager.RegionName="MainContentRegion"
                    Style="{StaticResource ContentRegionStyle}" />
            </Grid>

            <!--  状态栏  -->
            <StatusBar Grid.Row="2" Background="{DynamicResource MaterialDesignCardBackground}">
                <StatusBarItem>
                    <TextBlock Name="StatusText" Text="就绪" />
                </StatusBarItem>
                <Separator />
                <StatusBarItem HorizontalAlignment="Right">
                    <TextBlock Name="TimeText" Text="{Binding CurrentTime, StringFormat='yyyy-MM-dd HH:mm:ss'}" />
                </StatusBarItem>
            </StatusBar>
        </Grid>
    </Border>
</Window>
