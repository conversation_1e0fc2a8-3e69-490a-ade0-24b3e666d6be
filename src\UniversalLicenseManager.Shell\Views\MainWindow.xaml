<Window x:Class="UniversalLicenseManager.Shell.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:prism="http://prismlibrary.com/"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        prism:ViewModelLocator.AutoWireViewModel="True"
        Style="{StaticResource MainWindowStyle}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <WindowChrome.WindowChrome>
        <WindowChrome CaptionHeight="40" 
                      ResizeBorderThickness="5" 
                      UseAeroCaptionButtons="False" />
    </WindowChrome.WindowChrome>

    <Border Background="{DynamicResource MaterialDesignPaper}" 
            BorderBrush="{DynamicResource PrimaryHueMidBrush}" 
            BorderThickness="1"
            CornerRadius="8">
        <Grid>
            <Grid.RowDefinitions>
                <!-- 标题栏 -->
                <RowDefinition Height="40"/>
                <!-- 主内容区域 -->
                <RowDefinition Height="*"/>
                <!-- 状态栏 -->
                <RowDefinition Height="30"/>
            </Grid.RowDefinitions>

            <!-- 自定义标题栏 -->
            <Grid Grid.Row="0" Style="{StaticResource TitleBarStyle}">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 应用程序图标和标题 -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="10,0">
                    <materialDesign:PackIcon Kind="Security" 
                                           Foreground="White" 
                                           Width="20" Height="20" 
                                           VerticalAlignment="Center"/>
                    <TextBlock Text="Universal License Manager" 
                             Foreground="White" 
                             FontWeight="Medium" 
                             VerticalAlignment="Center" 
                             Margin="8,0,0,0"/>
                </StackPanel>

                <!-- 窗口控制按钮 -->
                <StackPanel Grid.Column="2" Orientation="Horizontal" WindowChrome.IsHitTestVisibleInChrome="True">
                    <Button Name="MinimizeButton" 
                            Style="{StaticResource MaterialDesignFlatButton}"
                            Width="40" Height="40"
                            Click="MinimizeButton_Click">
                        <materialDesign:PackIcon Kind="WindowMinimize" Foreground="White"/>
                    </Button>
                    <Button Name="MaximizeButton" 
                            Style="{StaticResource MaterialDesignFlatButton}"
                            Width="40" Height="40"
                            Click="MaximizeButton_Click">
                        <materialDesign:PackIcon Kind="WindowMaximize" Foreground="White"/>
                    </Button>
                    <Button Name="CloseButton" 
                            Style="{StaticResource MaterialDesignFlatButton}"
                            Width="40" Height="40"
                            Click="CloseButton_Click">
                        <materialDesign:PackIcon Kind="WindowClose" Foreground="White"/>
                    </Button>
                </StackPanel>
            </Grid>

            <!-- 主内容区域 -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <!-- 导航菜单 -->
                    <ColumnDefinition Width="280"/>
                    <!-- 内容区域 -->
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 左侧导航菜单 -->
                <Border Grid.Column="0" 
                        Background="{DynamicResource MaterialDesignCardBackground}"
                        BorderBrush="{DynamicResource MaterialDesignDivider}"
                        BorderThickness="0,0,1,0">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <ListBox Name="NavigationMenu" 
                                 Style="{StaticResource NavigationMenuStyle}"
                                 SelectionChanged="NavigationMenu_SelectionChanged">
                            <ListBoxItem>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Dashboard" Width="20" Height="20" Margin="0,0,16,0"/>
                                    <TextBlock Text="仪表板" VerticalAlignment="Center"/>
                                </StackPanel>
                            </ListBoxItem>
                            <ListBoxItem>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Fingerprint" Width="20" Height="20" Margin="0,0,16,0"/>
                                    <TextBlock Text="设备指纹管理" VerticalAlignment="Center"/>
                                </StackPanel>
                            </ListBoxItem>
                            <ListBoxItem>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Key" Width="20" Height="20" Margin="0,0,16,0"/>
                                    <TextBlock Text="License生成" VerticalAlignment="Center"/>
                                </StackPanel>
                            </ListBoxItem>
                            <ListBoxItem>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Settings" Width="20" Height="20" Margin="0,0,16,0"/>
                                    <TextBlock Text="授权配置" VerticalAlignment="Center"/>
                                </StackPanel>
                            </ListBoxItem>
                            <ListBoxItem>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="CodeBraces" Width="20" Height="20" Margin="0,0,16,0"/>
                                    <TextBlock Text="开发者工具" VerticalAlignment="Center"/>
                                </StackPanel>
                            </ListBoxItem>
                        </ListBox>
                    </ScrollViewer>
                </Border>

                <!-- 右侧内容区域 -->
                <ContentControl Grid.Column="1" 
                                Name="MainContentRegion"
                                prism:RegionManager.RegionName="MainContentRegion"
                                Style="{StaticResource ContentRegionStyle}"/>
            </Grid>

            <!-- 状态栏 -->
            <StatusBar Grid.Row="2" Background="{DynamicResource MaterialDesignCardBackground}">
                <StatusBarItem>
                    <TextBlock Name="StatusText" Text="就绪" />
                </StatusBarItem>
                <Separator/>
                <StatusBarItem HorizontalAlignment="Right">
                    <TextBlock Name="TimeText" Text="{Binding CurrentTime, StringFormat='yyyy-MM-dd HH:mm:ss'}" />
                </StatusBarItem>
            </StatusBar>
        </Grid>
    </Border>
</Window>
