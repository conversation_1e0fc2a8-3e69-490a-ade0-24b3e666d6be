using System;

namespace UniversalLicenseManager.Core.Services
{
    /// <summary>
    /// 日志级别枚举
    /// </summary>
    public enum LogLevel
    {
        Debug,
        Info,
        Warning,
        Error,
        Fatal
    }

    /// <summary>
    /// 日志服务接口
    /// 提供应用程序日志记录功能
    /// </summary>
    public interface ILogService
    {
        /// <summary>
        /// 记录调试信息
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常信息（可选）</param>
        void Debug(string message, Exception exception = null);

        /// <summary>
        /// 记录一般信息
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常信息（可选）</param>
        void Info(string message, Exception exception = null);

        /// <summary>
        /// 记录警告信息
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常信息（可选）</param>
        void Warning(string message, Exception exception = null);

        /// <summary>
        /// 记录错误信息
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常信息（可选）</param>
        void Error(string message, Exception exception = null);

        /// <summary>
        /// 记录致命错误信息
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常信息（可选）</param>
        void Fatal(string message, Exception exception = null);

        /// <summary>
        /// 记录指定级别的日志
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常信息（可选）</param>
        void Log(LogLevel level, string message, Exception exception = null);

        /// <summary>
        /// 设置日志级别
        /// </summary>
        /// <param name="level">最低日志级别</param>
        void SetLogLevel(LogLevel level);

        /// <summary>
        /// 清空日志文件
        /// </summary>
        void ClearLogs();

        /// <summary>
        /// 获取日志文件路径
        /// </summary>
        /// <returns>日志文件路径</returns>
        string GetLogFilePath();
    }
}
