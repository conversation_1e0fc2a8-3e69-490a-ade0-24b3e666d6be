{"version": 2, "dgSpecHash": "axg37fmJB/E=", "success": true, "projectFilePath": "D:\\00 Crack\\src\\UniversalLicenseManager.DeviceFingerprint\\UniversalLicenseManager.DeviceFingerprint.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\materialdesigncolors\\2.1.4\\materialdesigncolors.2.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\materialdesignthemes\\4.9.0\\materialdesignthemes.4.9.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.xaml.behaviors.wpf\\1.1.39\\microsoft.xaml.behaviors.wpf.1.1.39.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.3\\newtonsoft.json.13.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\prism.core\\8.1.97\\prism.core.8.1.97.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\prism.wpf\\8.1.97\\prism.wpf.8.1.97.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.codedom\\7.0.0\\system.codedom.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.management\\7.0.0\\system.management.7.0.0.nupkg.sha512"], "logs": []}