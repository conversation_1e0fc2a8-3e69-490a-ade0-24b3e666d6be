using System.Collections.Generic;
using System.Threading.Tasks;

namespace UniversalLicenseManager.Core.Services
{
    /// <summary>
    /// 配置服务接口
    /// 提供应用程序配置管理功能
    /// </summary>
    public interface IConfigurationService
    {
        /// <summary>
        /// 获取配置值
        /// </summary>
        /// <typeparam name="T">配置值类型</typeparam>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        T GetValue<T>(string key, T defaultValue = default(T));

        /// <summary>
        /// 设置配置值
        /// </summary>
        /// <typeparam name="T">配置值类型</typeparam>
        /// <param name="key">配置键</param>
        /// <param name="value">配置值</param>
        void SetValue<T>(string key, T value);

        /// <summary>
        /// 检查配置键是否存在
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>是否存在</returns>
        bool Has<PERSON>ey(string key);

        /// <summary>
        /// 删除配置项
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>是否成功</returns>
        bool RemoveKey(string key);

        /// <summary>
        /// 获取所有配置键
        /// </summary>
        /// <returns>配置键列表</returns>
        List<string> GetAllKeys();

        /// <summary>
        /// 保存配置到文件
        /// </summary>
        /// <returns>是否成功</returns>
        Task<bool> SaveAsync();

        /// <summary>
        /// 从文件加载配置
        /// </summary>
        /// <returns>是否成功</returns>
        Task<bool> LoadAsync();

        /// <summary>
        /// 重置所有配置为默认值
        /// </summary>
        void ResetToDefaults();

        /// <summary>
        /// 导出配置到指定文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否成功</returns>
        Task<bool> ExportAsync(string filePath);

        /// <summary>
        /// 从指定文件导入配置
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="overwrite">是否覆盖现有配置</param>
        /// <returns>是否成功</returns>
        Task<bool> ImportAsync(string filePath, bool overwrite = false);

        /// <summary>
        /// 获取配置文件路径
        /// </summary>
        /// <returns>配置文件路径</returns>
        string GetConfigFilePath();
    }
}
