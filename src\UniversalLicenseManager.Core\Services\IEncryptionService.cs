using System;

namespace UniversalLicenseManager.Core.Services
{
    /// <summary>
    /// 加密服务接口
    /// 提供各种加密和解密功能
    /// </summary>
    public interface IEncryptionService
    {
        /// <summary>
        /// 生成RSA密钥对
        /// </summary>
        /// <param name="keySize">密钥长度（默认2048位）</param>
        /// <returns>包含公钥和私钥的元组</returns>
        (string PublicKey, string PrivateKey) GenerateRSAKeyPair(int keySize = 2048);

        /// <summary>
        /// RSA加密
        /// </summary>
        /// <param name="plainText">明文</param>
        /// <param name="publicKey">公钥</param>
        /// <returns>加密后的Base64字符串</returns>
        string RSAEncrypt(string plainText, string publicKey);

        /// <summary>
        /// RSA解密
        /// </summary>
        /// <param name="cipherText">密文（Base64字符串）</param>
        /// <param name="privateKey">私钥</param>
        /// <returns>解密后的明文</returns>
        string RSADecrypt(string cipherText, string privateKey);

        /// <summary>
        /// AES加密
        /// </summary>
        /// <param name="plainText">明文</param>
        /// <param name="key">密钥</param>
        /// <param name="iv">初始化向量</param>
        /// <returns>加密后的Base64字符串</returns>
        string AESEncrypt(string plainText, string key, string iv = null);

        /// <summary>
        /// AES解密
        /// </summary>
        /// <param name="cipherText">密文（Base64字符串）</param>
        /// <param name="key">密钥</param>
        /// <param name="iv">初始化向量</param>
        /// <returns>解密后的明文</returns>
        string AESDecrypt(string cipherText, string key, string iv = null);

        /// <summary>
        /// 计算哈希值
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <param name="algorithm">哈希算法（MD5、SHA1、SHA256、SHA512）</param>
        /// <returns>哈希值（十六进制字符串）</returns>
        string ComputeHash(string input, string algorithm = "SHA256");

        /// <summary>
        /// 生成随机密钥
        /// </summary>
        /// <param name="length">密钥长度</param>
        /// <returns>随机密钥</returns>
        string GenerateRandomKey(int length = 32);

        /// <summary>
        /// 验证哈希值
        /// </summary>
        /// <param name="input">原始输入</param>
        /// <param name="hash">要验证的哈希值</param>
        /// <param name="algorithm">哈希算法</param>
        /// <returns>是否匹配</returns>
        bool VerifyHash(string input, string hash, string algorithm = "SHA256");

        /// <summary>
        /// 数字签名
        /// </summary>
        /// <param name="data">要签名的数据</param>
        /// <param name="privateKey">私钥</param>
        /// <returns>数字签名（Base64字符串）</returns>
        string SignData(string data, string privateKey);

        /// <summary>
        /// 验证数字签名
        /// </summary>
        /// <param name="data">原始数据</param>
        /// <param name="signature">数字签名</param>
        /// <param name="publicKey">公钥</param>
        /// <returns>签名是否有效</returns>
        bool VerifySignature(string data, string signature, string publicKey);
    }
}
