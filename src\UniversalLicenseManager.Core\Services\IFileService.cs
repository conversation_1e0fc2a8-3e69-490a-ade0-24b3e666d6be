using System.Collections.Generic;
using System.Threading.Tasks;

namespace UniversalLicenseManager.Core.Services
{
    /// <summary>
    /// 文件服务接口
    /// 提供文件读写和管理功能
    /// </summary>
    public interface IFileService
    {
        /// <summary>
        /// 读取文本文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件内容</returns>
        Task<string> ReadTextFileAsync(string filePath);

        /// <summary>
        /// 写入文本文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="content">文件内容</param>
        /// <returns>是否成功</returns>
        Task<bool> WriteTextFileAsync(string filePath, string content);

        /// <summary>
        /// 读取二进制文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件字节数组</returns>
        Task<byte[]> ReadBinaryFileAsync(string filePath);

        /// <summary>
        /// 写入二进制文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="data">文件数据</param>
        /// <returns>是否成功</returns>
        Task<bool> WriteBinaryFileAsync(string filePath, byte[] data);

        /// <summary>
        /// 检查文件是否存在
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否存在</returns>
        bool FileExists(string filePath);

        /// <summary>
        /// 检查目录是否存在
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <returns>是否存在</returns>
        bool DirectoryExists(string directoryPath);

        /// <summary>
        /// 创建目录
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <returns>是否成功</returns>
        bool CreateDirectory(string directoryPath);

        /// <summary>
        /// 删除文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否成功</returns>
        bool DeleteFile(string filePath);

        /// <summary>
        /// 复制文件
        /// </summary>
        /// <param name="sourceFilePath">源文件路径</param>
        /// <param name="destinationFilePath">目标文件路径</param>
        /// <param name="overwrite">是否覆盖</param>
        /// <returns>是否成功</returns>
        bool CopyFile(string sourceFilePath, string destinationFilePath, bool overwrite = false);

        /// <summary>
        /// 移动文件
        /// </summary>
        /// <param name="sourceFilePath">源文件路径</param>
        /// <param name="destinationFilePath">目标文件路径</param>
        /// <returns>是否成功</returns>
        bool MoveFile(string sourceFilePath, string destinationFilePath);

        /// <summary>
        /// 获取目录中的文件列表
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <param name="searchPattern">搜索模式</param>
        /// <returns>文件路径列表</returns>
        List<string> GetFiles(string directoryPath, string searchPattern = "*.*");

        /// <summary>
        /// 获取文件大小
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件大小（字节）</returns>
        long GetFileSize(string filePath);

        /// <summary>
        /// 获取文件扩展名
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件扩展名</returns>
        string GetFileExtension(string filePath);

        /// <summary>
        /// 获取文件名（不含扩展名）
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件名</returns>
        string GetFileNameWithoutExtension(string filePath);
    }
}
