using Prism.Ioc;
using Prism.Modularity;
using System.Windows;
using UniversalLicenseManager.Shell.Views;
using UniversalLicenseManager.Core;
using UniversalLicenseManager.DeviceFingerprint;
using UniversalLicenseManager.LicenseGeneration;
using UniversalLicenseManager.Configuration;
using UniversalLicenseManager.DeveloperTools;
using System;

namespace UniversalLicenseManager.Shell
{
    /// <summary>
    /// Universal License Manager 应用程序入口
    /// 基于Prism框架的模块化WPF应用程序
    /// </summary>
    public partial class App
    {
        /// <summary>
        /// 创建Shell窗口
        /// </summary>
        /// <returns>主窗口实例</returns>
        protected override Window CreateShell()
        {
            return Container.Resolve<MainWindow>();
        }

        /// <summary>
        /// 注册应用程序类型
        /// 在此方法中注册应用程序级别的服务和类型
        /// </summary>
        /// <param name="containerRegistry">容器注册器</param>
        protected override void RegisterTypes(IContainerRegistry containerRegistry)
        {
            // 注册Shell相关的ViewModels和Views
            containerRegistry.RegisterForNavigation<Views.DashboardView>();

            // 注册全局服务（如果需要）
            // containerRegistry.RegisterSingleton<IApplicationService, ApplicationService>();
        }

        /// <summary>
        /// 配置模块目录
        /// 定义应用程序要加载的模块
        /// </summary>
        /// <param name="moduleCatalog">模块目录</param>
        protected override void ConfigureModuleCatalog(IModuleCatalog moduleCatalog)
        {
            // 按照依赖顺序添加模块
            // Core模块必须首先加载，因为其他模块依赖于它
            moduleCatalog.AddModule<CoreModule>();
            
            // 功能模块可以并行加载
            moduleCatalog.AddModule<DeviceFingerprintModule>();
            moduleCatalog.AddModule<LicenseGenerationModule>();
            moduleCatalog.AddModule<ConfigurationModule>();
            moduleCatalog.AddModule<DeveloperToolsModule>();
        }

        /// <summary>
        /// 应用程序启动时的初始化逻辑
        /// </summary>
        protected override void OnInitialized()
        {
            // 设置应用程序标题和图标
            if (MainWindow != null)
            {
                MainWindow.Title = "Universal License Manager - 通用软件注册机";
            }

            base.OnInitialized();
        }

        /// <summary>
        /// 处理未捕获的异常
        /// </summary>
        /// <param name="e">启动事件参数</param>
        protected override void OnStartup(StartupEventArgs e)
        {
            // 设置全局异常处理
            AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;
            DispatcherUnhandledException += OnDispatcherUnhandledException;

            base.OnStartup(e);
        }

        /// <summary>
        /// 处理UI线程未捕获的异常
        /// </summary>
        private void OnDispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
        {
            MessageBox.Show($"应用程序发生未处理的异常：\n{e.Exception.Message}", 
                          "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            e.Handled = true;
        }

        /// <summary>
        /// 处理非UI线程未捕获的异常
        /// </summary>
        private void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            if (e.ExceptionObject is Exception exception)
            {
                MessageBox.Show($"应用程序发生严重错误：\n{exception.Message}", 
                              "严重错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
