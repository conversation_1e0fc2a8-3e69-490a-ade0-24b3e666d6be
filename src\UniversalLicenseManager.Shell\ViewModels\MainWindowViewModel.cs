using Prism.Mvvm;
using System;
using System.Windows.Threading;

namespace UniversalLicenseManager.Shell.ViewModels
{
    /// <summary>
    /// 主窗口ViewModel
    /// 负责主窗口的业务逻辑和数据绑定
    /// </summary>
    public class MainWindowViewModel : BindableBase
    {
        private string _title = "Universal License Manager - 通用软件注册机";
        private string _currentTime;
        private DispatcherTimer _timer;

        /// <summary>
        /// 窗口标题
        /// </summary>
        public string Title
        {
            get { return _title; }
            set { SetProperty(ref _title, value); }
        }

        /// <summary>
        /// 当前时间
        /// </summary>
        public string CurrentTime
        {
            get { return _currentTime; }
            set { SetProperty(ref _currentTime, value); }
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        public MainWindowViewModel()
        {
            InitializeTimer();
        }

        /// <summary>
        /// 初始化时间定时器
        /// </summary>
        private void InitializeTimer()
        {
            _timer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(1)
            };
            _timer.Tick += Timer_Tick;
            _timer.Start();

            // 立即更新一次时间
            UpdateCurrentTime();
        }

        /// <summary>
        /// 定时器Tick事件
        /// </summary>
        private void Timer_Tick(object sender, EventArgs e)
        {
            UpdateCurrentTime();
        }

        /// <summary>
        /// 更新当前时间
        /// </summary>
        private void UpdateCurrentTime()
        {
            CurrentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _timer?.Stop();
            _timer = null;
        }
    }
}
