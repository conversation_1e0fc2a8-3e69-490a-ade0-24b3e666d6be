using System;
using System.IO;

namespace UniversalLicenseManager.Core.Services
{
    /// <summary>
    /// 日志服务实现
    /// 提供基础的文件日志记录功能
    /// </summary>
    public class LogService : ILogService
    {
        private readonly string _logFilePath;
        private LogLevel _minLogLevel = LogLevel.Info;
        private readonly object _lockObject = new object();

        /// <summary>
        /// 构造函数
        /// </summary>
        public LogService()
        {
            var logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
            if (!Directory.Exists(logDirectory))
            {
                Directory.CreateDirectory(logDirectory);
            }
            
            _logFilePath = Path.Combine(logDirectory, $"app_{DateTime.Now:yyyyMMdd}.log");
        }

        /// <summary>
        /// 记录调试信息
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常信息</param>
        public void Debug(string message, Exception exception = null)
        {
            Log(LogLevel.Debug, message, exception);
        }

        /// <summary>
        /// 记录一般信息
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常信息</param>
        public void Info(string message, Exception exception = null)
        {
            Log(LogLevel.Info, message, exception);
        }

        /// <summary>
        /// 记录警告信息
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常信息</param>
        public void Warning(string message, Exception exception = null)
        {
            Log(LogLevel.Warning, message, exception);
        }

        /// <summary>
        /// 记录错误信息
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常信息</param>
        public void Error(string message, Exception exception = null)
        {
            Log(LogLevel.Error, message, exception);
        }

        /// <summary>
        /// 记录致命错误信息
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常信息</param>
        public void Fatal(string message, Exception exception = null)
        {
            Log(LogLevel.Fatal, message, exception);
        }

        /// <summary>
        /// 记录指定级别的日志
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常信息</param>
        public void Log(LogLevel level, string message, Exception exception = null)
        {
            if (level < _minLogLevel)
                return;

            try
            {
                var logEntry = FormatLogEntry(level, message, exception);
                
                lock (_lockObject)
                {
                    File.AppendAllText(_logFilePath, logEntry + Environment.NewLine);
                }
            }
            catch
            {
                // 忽略日志记录失败的异常，避免影响主程序
            }
        }

        /// <summary>
        /// 设置日志级别
        /// </summary>
        /// <param name="level">最低日志级别</param>
        public void SetLogLevel(LogLevel level)
        {
            _minLogLevel = level;
        }

        /// <summary>
        /// 清空日志文件
        /// </summary>
        public void ClearLogs()
        {
            try
            {
                lock (_lockObject)
                {
                    if (File.Exists(_logFilePath))
                    {
                        File.WriteAllText(_logFilePath, string.Empty);
                    }
                }
            }
            catch
            {
                // 忽略清空日志失败的异常
            }
        }

        /// <summary>
        /// 获取日志文件路径
        /// </summary>
        /// <returns>日志文件路径</returns>
        public string GetLogFilePath()
        {
            return _logFilePath;
        }

        /// <summary>
        /// 格式化日志条目
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="message">日志消息</param>
        /// <param name="exception">异常信息</param>
        /// <returns>格式化后的日志条目</returns>
        private string FormatLogEntry(LogLevel level, string message, Exception exception)
        {
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
            var levelString = level.ToString().ToUpper().PadRight(7);
            
            var logEntry = $"[{timestamp}] [{levelString}] {message}";
            
            if (exception != null)
            {
                logEntry += Environment.NewLine + $"Exception: {exception}";
            }
            
            return logEntry;
        }
    }
}
