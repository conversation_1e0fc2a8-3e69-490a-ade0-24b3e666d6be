using System;
using System.Collections.Generic;
using System.Linq;
using System.Management;
using System.Net.NetworkInformation;
using System.Threading.Tasks;

namespace UniversalLicenseManager.Core.Utilities
{
    /// <summary>
    /// 硬件信息帮助类
    /// 提供获取系统硬件信息的静态方法
    /// </summary>
    public static class HardwareInfoHelper
    {
        /// <summary>
        /// 获取CPU序列号
        /// </summary>
        /// <returns>CPU序列号</returns>
        public static async Task<string> GetCpuSerialNumberAsync()
        {
            return await Task.Run(() =>
            {
                try
                {
                    using (var searcher = new ManagementObjectSearcher("SELECT ProcessorId FROM Win32_Processor"))
                    {
                        var collection = searcher.Get();
                        var processorId = collection.Cast<ManagementObject>()
                            .Select(mo => mo["ProcessorId"]?.ToString())
                            .FirstOrDefault(id => !string.IsNullOrEmpty(id));
                        
                        return processorId ?? "UNKNOWN_CPU";
                    }
                }
                catch (Exception ex)
                {
                    return $"ERROR_CPU_{ex.GetHashCode():X}";
                }
            });
        }

        /// <summary>
        /// 获取主板序列号
        /// </summary>
        /// <returns>主板序列号</returns>
        public static async Task<string> GetMotherboardSerialNumberAsync()
        {
            return await Task.Run(() =>
            {
                try
                {
                    using (var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_BaseBoard"))
                    {
                        var collection = searcher.Get();
                        var serialNumber = collection.Cast<ManagementObject>()
                            .Select(mo => mo["SerialNumber"]?.ToString())
                            .FirstOrDefault(sn => !string.IsNullOrEmpty(sn) && sn != "To be filled by O.E.M.");
                        
                        return serialNumber ?? "UNKNOWN_MOTHERBOARD";
                    }
                }
                catch (Exception ex)
                {
                    return $"ERROR_MOTHERBOARD_{ex.GetHashCode():X}";
                }
            });
        }

        /// <summary>
        /// 获取硬盘序列号
        /// </summary>
        /// <returns>硬盘序列号</returns>
        public static async Task<string> GetHardDiskSerialNumberAsync()
        {
            return await Task.Run(() =>
            {
                try
                {
                    using (var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_DiskDrive WHERE MediaType='Fixed hard disk media'"))
                    {
                        var collection = searcher.Get();
                        var serialNumber = collection.Cast<ManagementObject>()
                            .Select(mo => mo["SerialNumber"]?.ToString()?.Trim())
                            .FirstOrDefault(sn => !string.IsNullOrEmpty(sn));
                        
                        return serialNumber ?? "UNKNOWN_HARDDISK";
                    }
                }
                catch (Exception ex)
                {
                    return $"ERROR_HARDDISK_{ex.GetHashCode():X}";
                }
            });
        }

        /// <summary>
        /// 获取MAC地址
        /// </summary>
        /// <returns>MAC地址</returns>
        public static async Task<string> GetMacAddressAsync()
        {
            return await Task.Run(() =>
            {
                try
                {
                    var networkInterfaces = NetworkInterface.GetAllNetworkInterfaces()
                        .Where(ni => ni.OperationalStatus == OperationalStatus.Up && 
                                   ni.NetworkInterfaceType != NetworkInterfaceType.Loopback &&
                                   ni.NetworkInterfaceType != NetworkInterfaceType.Tunnel)
                        .OrderBy(ni => ni.NetworkInterfaceType)
                        .ToList();

                    var macAddress = networkInterfaces
                        .Select(ni => ni.GetPhysicalAddress().ToString())
                        .FirstOrDefault(mac => !string.IsNullOrEmpty(mac) && mac != "000000000000");

                    return macAddress ?? "UNKNOWN_MAC";
                }
                catch (Exception ex)
                {
                    return $"ERROR_MAC_{ex.GetHashCode():X}";
                }
            });
        }

        /// <summary>
        /// 获取系统UUID
        /// </summary>
        /// <returns>系统UUID</returns>
        public static async Task<string> GetSystemUuidAsync()
        {
            return await Task.Run(() =>
            {
                try
                {
                    using (var searcher = new ManagementObjectSearcher("SELECT UUID FROM Win32_ComputerSystemProduct"))
                    {
                        var collection = searcher.Get();
                        var uuid = collection.Cast<ManagementObject>()
                            .Select(mo => mo["UUID"]?.ToString())
                            .FirstOrDefault(u => !string.IsNullOrEmpty(u));
                        
                        return uuid ?? "UNKNOWN_UUID";
                    }
                }
                catch (Exception ex)
                {
                    return $"ERROR_UUID_{ex.GetHashCode():X}";
                }
            });
        }

        /// <summary>
        /// 获取计算机名称
        /// </summary>
        /// <returns>计算机名称</returns>
        public static string GetComputerName()
        {
            try
            {
                return Environment.MachineName ?? "UNKNOWN_COMPUTER";
            }
            catch (Exception ex)
            {
                return $"ERROR_COMPUTER_{ex.GetHashCode():X}";
            }
        }

        /// <summary>
        /// 获取操作系统信息
        /// </summary>
        /// <returns>操作系统信息</returns>
        public static async Task<string> GetOperatingSystemInfoAsync()
        {
            return await Task.Run(() =>
            {
                try
                {
                    using (var searcher = new ManagementObjectSearcher("SELECT Caption, Version FROM Win32_OperatingSystem"))
                    {
                        var collection = searcher.Get();
                        var osInfo = collection.Cast<ManagementObject>()
                            .Select(mo => $"{mo["Caption"]} {mo["Version"]}")
                            .FirstOrDefault();
                        
                        return osInfo ?? Environment.OSVersion.ToString();
                    }
                }
                catch (Exception ex)
                {
                    return $"ERROR_OS_{ex.GetHashCode():X}";
                }
            });
        }

        /// <summary>
        /// 获取BIOS序列号
        /// </summary>
        /// <returns>BIOS序列号</returns>
        public static async Task<string> GetBiosSerialNumberAsync()
        {
            return await Task.Run(() =>
            {
                try
                {
                    using (var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_BIOS"))
                    {
                        var collection = searcher.Get();
                        var serialNumber = collection.Cast<ManagementObject>()
                            .Select(mo => mo["SerialNumber"]?.ToString())
                            .FirstOrDefault(sn => !string.IsNullOrEmpty(sn));
                        
                        return serialNumber ?? "UNKNOWN_BIOS";
                    }
                }
                catch (Exception ex)
                {
                    return $"ERROR_BIOS_{ex.GetHashCode():X}";
                }
            });
        }

        /// <summary>
        /// 获取内存信息
        /// </summary>
        /// <returns>内存信息</returns>
        public static async Task<string> GetMemoryInfoAsync()
        {
            return await Task.Run(() =>
            {
                try
                {
                    using (var searcher = new ManagementObjectSearcher("SELECT SerialNumber, Capacity FROM Win32_PhysicalMemory"))
                    {
                        var collection = searcher.Get();
                        var memoryInfo = collection.Cast<ManagementObject>()
                            .Select(mo => $"{mo["SerialNumber"]}_{mo["Capacity"]}")
                            .Where(info => !string.IsNullOrEmpty(info) && info != "_")
                            .FirstOrDefault();
                        
                        return memoryInfo ?? "UNKNOWN_MEMORY";
                    }
                }
                catch (Exception ex)
                {
                    return $"ERROR_MEMORY_{ex.GetHashCode():X}";
                }
            });
        }

        /// <summary>
        /// 获取所有硬件信息
        /// </summary>
        /// <returns>硬件信息字典</returns>
        public static async Task<Dictionary<string, string>> GetAllHardwareInfoAsync()
        {
            var hardwareInfo = new Dictionary<string, string>();

            try
            {
                var tasks = new List<Task<KeyValuePair<string, string>>>
                {
                    GetCpuSerialNumberAsync().ContinueWith(t => new KeyValuePair<string, string>("CPU", t.Result)),
                    GetMotherboardSerialNumberAsync().ContinueWith(t => new KeyValuePair<string, string>("Motherboard", t.Result)),
                    GetHardDiskSerialNumberAsync().ContinueWith(t => new KeyValuePair<string, string>("HardDisk", t.Result)),
                    GetMacAddressAsync().ContinueWith(t => new KeyValuePair<string, string>("MacAddress", t.Result)),
                    GetSystemUuidAsync().ContinueWith(t => new KeyValuePair<string, string>("SystemUUID", t.Result)),
                    GetOperatingSystemInfoAsync().ContinueWith(t => new KeyValuePair<string, string>("OperatingSystem", t.Result)),
                    GetBiosSerialNumberAsync().ContinueWith(t => new KeyValuePair<string, string>("BIOS", t.Result)),
                    GetMemoryInfoAsync().ContinueWith(t => new KeyValuePair<string, string>("Memory", t.Result))
                };

                // 添加同步获取的信息
                hardwareInfo["ComputerName"] = GetComputerName();

                // 等待所有异步任务完成
                var results = await Task.WhenAll(tasks);
                
                foreach (var result in results)
                {
                    hardwareInfo[result.Key] = result.Value;
                }
            }
            catch (Exception ex)
            {
                hardwareInfo["Error"] = $"获取硬件信息时发生错误: {ex.Message}";
            }

            return hardwareInfo;
        }

        /// <summary>
        /// 检查硬件组件是否可用
        /// </summary>
        /// <param name="component">硬件组件名称</param>
        /// <returns>是否可用</returns>
        public static async Task<bool> IsHardwareComponentAvailableAsync(string component)
        {
            try
            {
                switch (component.ToUpper())
                {
                    case "CPU":
                        var cpu = await GetCpuSerialNumberAsync();
                        return !cpu.StartsWith("ERROR_") && cpu != "UNKNOWN_CPU";
                    
                    case "MOTHERBOARD":
                        var motherboard = await GetMotherboardSerialNumberAsync();
                        return !motherboard.StartsWith("ERROR_") && motherboard != "UNKNOWN_MOTHERBOARD";
                    
                    case "HARDDISK":
                        var hardDisk = await GetHardDiskSerialNumberAsync();
                        return !hardDisk.StartsWith("ERROR_") && hardDisk != "UNKNOWN_HARDDISK";
                    
                    case "MACADDRESS":
                        var mac = await GetMacAddressAsync();
                        return !mac.StartsWith("ERROR_") && mac != "UNKNOWN_MAC";
                    
                    case "SYSTEMUUID":
                        var uuid = await GetSystemUuidAsync();
                        return !uuid.StartsWith("ERROR_") && uuid != "UNKNOWN_UUID";
                    
                    case "BIOS":
                        var bios = await GetBiosSerialNumberAsync();
                        return !bios.StartsWith("ERROR_") && bios != "UNKNOWN_BIOS";
                    
                    case "MEMORY":
                        var memory = await GetMemoryInfoAsync();
                        return !memory.StartsWith("ERROR_") && memory != "UNKNOWN_MEMORY";
                    
                    default:
                        return false;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取可用的硬件组件列表
        /// </summary>
        /// <returns>可用的硬件组件列表</returns>
        public static async Task<List<string>> GetAvailableHardwareComponentsAsync()
        {
            var components = new List<string> { "CPU", "Motherboard", "HardDisk", "MacAddress", "SystemUUID", "BIOS", "Memory" };
            var availableComponents = new List<string>();

            foreach (var component in components)
            {
                if (await IsHardwareComponentAvailableAsync(component))
                {
                    availableComponents.Add(component);
                }
            }

            return availableComponents;
        }
    }
}
