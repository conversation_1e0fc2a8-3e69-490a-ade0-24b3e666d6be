using Prism.Ioc;
using Prism.Modularity;
using UniversalLicenseManager.Core.Services;

namespace UniversalLicenseManager.Core
{
    /// <summary>
    /// 核心业务逻辑模块
    /// 提供基础服务和通用功能
    /// </summary>
    public class CoreModule : IModule
    {
        /// <summary>
        /// 模块初始化
        /// </summary>
        /// <param name="containerProvider">容器提供者</param>
        public void OnInitialized(IContainerProvider containerProvider)
        {
            // 模块初始化逻辑
            // 可以在这里执行模块启动时需要的初始化操作
        }

        /// <summary>
        /// 注册模块类型
        /// </summary>
        /// <param name="containerRegistry">容器注册器</param>
        public void RegisterTypes(IContainerRegistry containerRegistry)
        {
            // 注册核心服务
            containerRegistry.RegisterSingleton<IEncryptionService, EncryptionService>();
            containerRegistry.RegisterSingleton<IFileService, FileService>();
            containerRegistry.RegisterSingleton<ILogService, LogService>();
            containerRegistry.RegisterSingleton<IConfigurationService, ConfigurationService>();
            
            // 注册其他核心服务接口和实现
            // containerRegistry.Register<IService, ServiceImplementation>();
        }
    }
}
