<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <AssemblyTitle>Universal License Manager Device Fingerprint Module</AssemblyTitle>
    <AssemblyDescription>设备指纹生成与管理模块</AssemblyDescription>
    <AssemblyCompany>Universal License Manager Team</AssemblyCompany>
    <AssemblyProduct>Universal License Manager</AssemblyProduct>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Prism.Wpf" Version="8.1.97" />
    <PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
    <PackageReference Include="MaterialDesignColors" Version="2.1.4" />
    <PackageReference Include="System.Management" Version="7.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\UniversalLicenseManager.Core\UniversalLicenseManager.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="ViewModels\" />
    <Folder Include="Views\" />
    <Folder Include="Services\" />
  </ItemGroup>

</Project>
