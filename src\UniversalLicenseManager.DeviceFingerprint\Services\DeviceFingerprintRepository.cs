using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using UniversalLicenseManager.Core.Models;
using UniversalLicenseManager.Core.Services;
using UniversalLicenseManager.Core.Utilities;
using UniversalLicenseManager.Core.Constants;

namespace UniversalLicenseManager.DeviceFingerprint.Services
{
    /// <summary>
    /// 设备指纹数据存储接口
    /// </summary>
    public interface IDeviceFingerprintRepository
    {
        /// <summary>
        /// 获取所有设备指纹
        /// </summary>
        /// <returns>设备指纹列表</returns>
        Task<List<Core.Models.DeviceFingerprint>> GetAllAsync();

        /// <summary>
        /// 根据ID获取设备指纹
        /// </summary>
        /// <param name="id">设备指纹ID</param>
        /// <returns>设备指纹</returns>
        Task<Core.Models.DeviceFingerprint> GetByIdAsync(string id);

        /// <summary>
        /// 保存设备指纹
        /// </summary>
        /// <param name="fingerprint">设备指纹</param>
        /// <returns>是否成功</returns>
        Task<bool> SaveAsync(Core.Models.DeviceFingerprint fingerprint);

        /// <summary>
        /// 删除设备指纹
        /// </summary>
        /// <param name="id">设备指纹ID</param>
        /// <returns>是否成功</returns>
        Task<bool> DeleteAsync(string id);

        /// <summary>
        /// 根据哈希值查找设备指纹
        /// </summary>
        /// <param name="hash">指纹哈希</param>
        /// <returns>设备指纹</returns>
        Task<Core.Models.DeviceFingerprint> GetByHashAsync(string hash);

        /// <summary>
        /// 保存所有设备指纹
        /// </summary>
        /// <returns>是否成功</returns>
        Task<bool> SaveAllAsync();
    }

    /// <summary>
    /// 设备指纹数据存储实现
    /// 基于JSON文件的简单存储
    /// </summary>
    public class DeviceFingerprintRepository : IDeviceFingerprintRepository
    {
        private readonly IFileService _fileService;
        private readonly ILogService _logService;
        private readonly string _dataFilePath;
        private List<Core.Models.DeviceFingerprint> _fingerprints;
        private readonly object _lockObject = new object();

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="fileService">文件服务</param>
        /// <param name="logService">日志服务</param>
        public DeviceFingerprintRepository(IFileService fileService, ILogService logService)
        {
            _fileService = fileService ?? throw new ArgumentNullException(nameof(fileService));
            _logService = logService ?? throw new ArgumentNullException(nameof(logService));

            var dataDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, ApplicationConstants.Paths.DataDirectory);
            _fileService.CreateDirectory(dataDirectory);
            _dataFilePath = Path.Combine(dataDirectory, ApplicationConstants.Paths.DeviceFingerprintDataFileName);

            _fingerprints = new List<Core.Models.DeviceFingerprint>();

            // 异步加载数据
            _ = LoadDataAsync();
        }

        /// <summary>
        /// 获取所有设备指纹
        /// </summary>
        /// <returns>设备指纹列表</returns>
        public async Task<List<Core.Models.DeviceFingerprint>> GetAllAsync()
        {
            await EnsureDataLoadedAsync();
            
            lock (_lockObject)
            {
                return new List<Core.Models.DeviceFingerprint>(_fingerprints);
            }
        }

        /// <summary>
        /// 根据ID获取设备指纹
        /// </summary>
        /// <param name="id">设备指纹ID</param>
        /// <returns>设备指纹</returns>
        public async Task<Core.Models.DeviceFingerprint> GetByIdAsync(string id)
        {
            await EnsureDataLoadedAsync();

            lock (_lockObject)
            {
                return _fingerprints.FirstOrDefault(f => f.Id == id);
            }
        }

        /// <summary>
        /// 保存设备指纹
        /// </summary>
        /// <param name="fingerprint">设备指纹</param>
        /// <returns>是否成功</returns>
        public async Task<bool> SaveAsync(Core.Models.DeviceFingerprint fingerprint)
        {
            try
            {
                await EnsureDataLoadedAsync();

                lock (_lockObject)
                {
                    var existingIndex = _fingerprints.FindIndex(f => f.Id == fingerprint.Id);
                    if (existingIndex >= 0)
                    {
                        _fingerprints[existingIndex] = fingerprint;
                    }
                    else
                    {
                        _fingerprints.Add(fingerprint);
                    }
                }

                await SaveAllAsync();
                _logService.Info($"保存设备指纹成功: {fingerprint.Id}");
                return true;
            }
            catch (Exception ex)
            {
                _logService.Error($"保存设备指纹失败: {fingerprint.Id}", ex);
                return false;
            }
        }

        /// <summary>
        /// 删除设备指纹
        /// </summary>
        /// <param name="id">设备指纹ID</param>
        /// <returns>是否成功</returns>
        public async Task<bool> DeleteAsync(string id)
        {
            try
            {
                await EnsureDataLoadedAsync();

                lock (_lockObject)
                {
                    var fingerprint = _fingerprints.FirstOrDefault(f => f.Id == id);
                    if (fingerprint != null)
                    {
                        _fingerprints.Remove(fingerprint);
                    }
                    else
                    {
                        return false;
                    }
                }

                await SaveAllAsync();
                _logService.Info($"删除设备指纹成功: {id}");
                return true;
            }
            catch (Exception ex)
            {
                _logService.Error($"删除设备指纹失败: {id}", ex);
                return false;
            }
        }

        /// <summary>
        /// 根据哈希值查找设备指纹
        /// </summary>
        /// <param name="hash">指纹哈希</param>
        /// <returns>设备指纹</returns>
        public async Task<Core.Models.DeviceFingerprint> GetByHashAsync(string hash)
        {
            await EnsureDataLoadedAsync();

            lock (_lockObject)
            {
                return _fingerprints.FirstOrDefault(f => string.Equals(f.FingerprintHash, hash, StringComparison.OrdinalIgnoreCase));
            }
        }

        /// <summary>
        /// 保存所有设备指纹
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> SaveAllAsync()
        {
            try
            {
                List<Core.Models.DeviceFingerprint> fingerprintsCopy;
                lock (_lockObject)
                {
                    fingerprintsCopy = new List<Core.Models.DeviceFingerprint>(_fingerprints);
                }

                var json = JsonHelper.Serialize(fingerprintsCopy);
                await _fileService.WriteTextFileAsync(_dataFilePath, json);

                _logService.Debug($"保存所有设备指纹成功，共 {fingerprintsCopy.Count} 个");
                return true;
            }
            catch (Exception ex)
            {
                _logService.Error("保存所有设备指纹失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 加载数据
        /// </summary>
        private async Task LoadDataAsync()
        {
            try
            {
                if (_fileService.FileExists(_dataFilePath))
                {
                    var json = await _fileService.ReadTextFileAsync(_dataFilePath);
                    var loadedFingerprints = JsonHelper.Deserialize<List<Core.Models.DeviceFingerprint>>(json);

                    lock (_lockObject)
                    {
                        _fingerprints = loadedFingerprints ?? new List<Core.Models.DeviceFingerprint>();
                    }

                    _logService.Info($"加载设备指纹数据成功，共 {_fingerprints.Count} 个");
                }
                else
                {
                    _logService.Info("设备指纹数据文件不存在，创建新的数据集合");
                }
            }
            catch (Exception ex)
            {
                _logService.Error("加载设备指纹数据失败", ex);
                lock (_lockObject)
                {
                    _fingerprints = new List<Core.Models.DeviceFingerprint>();
                }
            }
        }

        /// <summary>
        /// 确保数据已加载
        /// </summary>
        private async Task EnsureDataLoadedAsync()
        {
            if (_fingerprints == null)
            {
                await LoadDataAsync();
            }
        }
    }
}
