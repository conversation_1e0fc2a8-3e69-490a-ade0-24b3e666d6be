<UserControl
    x:Class="UniversalLicenseManager.DeviceFingerprint.Views.DeviceFingerprintView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:prism="http://prismlibrary.com/"
    prism:ViewModelLocator.AutoWireViewModel="True">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  页面标题  -->
        <TextBlock
            Grid.Row="0"
            Margin="0,0,0,20"
            Style="{StaticResource MaterialDesignHeadline4TextBlock}"
            Text="设备指纹管理" />

        <!--  操作面板  -->
        <materialDesign:Card
            Grid.Row="1"
            Margin="0,0,0,20"
            Padding="20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <!--  生成策略选择  -->
                <Grid Grid.Row="0" Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>

                    <TextBlock
                        Grid.Column="0"
                        Margin="0,0,10,0"
                        VerticalAlignment="Center"
                        Text="生成策略:" />
                    <ComboBox
                        Grid.Column="1"
                        Margin="0,0,20,0"
                        ItemsSource="{Binding AvailableStrategies}"
                        SelectedItem="{Binding SelectedStrategy}" />

                    <TextBlock
                        Grid.Column="2"
                        Margin="0,0,10,0"
                        VerticalAlignment="Center"
                        Text="哈希算法:" />
                    <ComboBox
                        Grid.Column="3"
                        ItemsSource="{Binding AvailableAlgorithms}"
                        SelectedItem="{Binding SelectedAlgorithm}" />
                </Grid>

                <!--  自定义组件选择  -->
                <GroupBox
                    Grid.Row="1"
                    Margin="0,0,0,15"
                    Header="自定义硬件组件">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>

                        <ItemsControl Grid.Row="0" ItemsSource="{Binding AvailableComponents}">
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <WrapPanel Orientation="Horizontal" />
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <CheckBox Margin="0,0,15,5" Content="{Binding}" />
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>

                        <Button
                            Grid.Row="1"
                            Margin="0,10,0,0"
                            HorizontalAlignment="Left"
                            Command="{Binding RefreshComponentsCommand}"
                            Content="刷新可用组件"
                            Style="{StaticResource MaterialDesignOutlinedButton}" />
                    </Grid>
                </GroupBox>

                <!--  操作按钮  -->
                <StackPanel Grid.Row="2" Orientation="Horizontal">
                    <Button
                        Margin="0,0,10,0"
                        Command="{Binding GenerateCurrentFingerprintCommand}"
                        Style="{StaticResource MaterialDesignRaisedButton}">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Margin="0,0,5,0" Kind="Fingerprint" />
                                <TextBlock Text="生成当前设备指纹" />
                            </StackPanel>
                        </Button.Content>
                    </Button>

                    <Button
                        Margin="0,0,10,0"
                        Command="{Binding GenerateCustomFingerprintCommand}"
                        Style="{StaticResource MaterialDesignRaisedButton}">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Margin="0,0,5,0" Kind="Tune" />
                                <TextBlock Text="生成自定义指纹" />
                            </StackPanel>
                        </Button.Content>
                    </Button>

                    <Button
                        Margin="0,0,10,0"
                        Command="{Binding ValidateFingerprintCommand}"
                        Style="{StaticResource MaterialDesignOutlinedButton}">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Margin="0,0,5,0" Kind="CheckCircle" />
                                <TextBlock Text="验证指纹" />
                            </StackPanel>
                        </Button.Content>
                    </Button>

                    <Button Command="{Binding ClearListCommand}" Style="{StaticResource MaterialDesignOutlinedButton}">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Margin="0,0,5,0" Kind="Delete" />
                                <TextBlock Text="清空列表" />
                            </StackPanel>
                        </Button.Content>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!--  主内容区域  -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*" />
                <ColumnDefinition Width="5" />
                <ColumnDefinition Width="3*" />
            </Grid.ColumnDefinitions>

            <!--  左侧：设备指纹列表  -->
            <materialDesign:Card Grid.Column="0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>

                    <TextBlock
                        Grid.Row="0"
                        Margin="20,20,20,10"
                        Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                        Text="设备指纹列表" />

                    <DataGrid
                        Grid.Row="1"
                        Margin="20,0,20,10"
                        AutoGenerateColumns="False"
                        CanUserAddRows="False"
                        CanUserDeleteRows="False"
                        IsReadOnly="True"
                        ItemsSource="{Binding Fingerprints}"
                        SelectedItem="{Binding SelectedFingerprint}">
                        <DataGrid.Columns>
                            <DataGridTextColumn
                                Width="120"
                                Binding="{Binding DeviceName}"
                                Header="设备名称" />
                            <DataGridTextColumn
                                Width="80"
                                Binding="{Binding Algorithm}"
                                Header="算法" />
                            <DataGridTextColumn
                                Width="200"
                                Binding="{Binding FingerprintHash}"
                                Header="指纹哈希">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="TextTrimming" Value="CharacterEllipsis" />
                                        <Setter Property="ToolTip" Value="{Binding FingerprintHash}" />
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn
                                Width="120"
                                Binding="{Binding CreatedAt, StringFormat='yyyy-MM-dd HH:mm'}"
                                Header="创建时间" />
                        </DataGrid.Columns>
                    </DataGrid>

                    <StackPanel
                        Grid.Row="2"
                        Margin="20,0,20,20"
                        Orientation="Horizontal">
                        <Button
                            Margin="0,0,10,0"
                            Command="{Binding ExportFingerprintCommand}"
                            Content="导出"
                            Style="{StaticResource MaterialDesignOutlinedButton}" />
                        <Button
                            Margin="0,0,10,0"
                            Command="{Binding ImportFingerprintCommand}"
                            Content="导入"
                            Style="{StaticResource MaterialDesignOutlinedButton}" />
                        <Button
                            Command="{Binding DeleteFingerprintCommand}"
                            Content="删除"
                            Style="{StaticResource MaterialDesignOutlinedButton}" />
                    </StackPanel>
                </Grid>
            </materialDesign:Card>

            <GridSplitter
                Grid.Column="1"
                HorizontalAlignment="Stretch"
                VerticalAlignment="Stretch"
                Background="{DynamicResource MaterialDesignDivider}" />

            <!--  右侧：详细信息和报告  -->
            <materialDesign:Card Grid.Column="2">
                <TabControl>
                    <TabItem Header="详细信息">
                        <ScrollViewer Margin="20">
                            <StackPanel>
                                <TextBlock
                                    Margin="0,0,0,15"
                                    Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                    Text="设备指纹详细信息" />

                                <Grid DataContext="{Binding SelectedFingerprint}">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>

                                    <TextBlock
                                        Grid.Row="0"
                                        Grid.Column="0"
                                        Margin="0,0,10,5"
                                        FontWeight="Bold"
                                        Text="ID:" />
                                    <TextBlock
                                        Grid.Row="0"
                                        Grid.Column="1"
                                        Margin="0,0,0,5"
                                        Text="{Binding Id}" />

                                    <TextBlock
                                        Grid.Row="1"
                                        Grid.Column="0"
                                        Margin="0,0,10,5"
                                        FontWeight="Bold"
                                        Text="设备名称:" />
                                    <TextBlock
                                        Grid.Row="1"
                                        Grid.Column="1"
                                        Margin="0,0,0,5"
                                        Text="{Binding DeviceName}" />

                                    <TextBlock
                                        Grid.Row="2"
                                        Grid.Column="0"
                                        Margin="0,0,10,5"
                                        FontWeight="Bold"
                                        Text="CPU序列号:" />
                                    <TextBlock
                                        Grid.Row="2"
                                        Grid.Column="1"
                                        Margin="0,0,0,5"
                                        Text="{Binding CpuId}" />

                                    <TextBlock
                                        Grid.Row="3"
                                        Grid.Column="0"
                                        Margin="0,0,10,5"
                                        FontWeight="Bold"
                                        Text="主板序列号:" />
                                    <TextBlock
                                        Grid.Row="3"
                                        Grid.Column="1"
                                        Margin="0,0,0,5"
                                        Text="{Binding MotherboardId}" />

                                    <TextBlock
                                        Grid.Row="4"
                                        Grid.Column="0"
                                        Margin="0,0,10,5"
                                        FontWeight="Bold"
                                        Text="硬盘序列号:" />
                                    <TextBlock
                                        Grid.Row="4"
                                        Grid.Column="1"
                                        Margin="0,0,0,5"
                                        Text="{Binding HardDiskId}" />

                                    <TextBlock
                                        Grid.Row="5"
                                        Grid.Column="0"
                                        Margin="0,0,10,5"
                                        FontWeight="Bold"
                                        Text="MAC地址:" />
                                    <TextBlock
                                        Grid.Row="5"
                                        Grid.Column="1"
                                        Margin="0,0,0,5"
                                        Text="{Binding MacAddress}" />

                                    <TextBlock
                                        Grid.Row="6"
                                        Grid.Column="0"
                                        Margin="0,0,10,5"
                                        FontWeight="Bold"
                                        Text="系统UUID:" />
                                    <TextBlock
                                        Grid.Row="6"
                                        Grid.Column="1"
                                        Margin="0,0,0,5"
                                        Text="{Binding SystemUuid}" />

                                    <TextBlock
                                        Grid.Row="7"
                                        Grid.Column="0"
                                        Margin="0,0,10,5"
                                        FontWeight="Bold"
                                        Text="哈希算法:" />
                                    <TextBlock
                                        Grid.Row="7"
                                        Grid.Column="1"
                                        Margin="0,0,0,5"
                                        Text="{Binding Algorithm}" />

                                    <TextBlock
                                        Grid.Row="8"
                                        Grid.Column="0"
                                        Margin="0,0,10,5"
                                        FontWeight="Bold"
                                        Text="指纹哈希:" />
                                    <TextBlock
                                        Grid.Row="8"
                                        Grid.Column="1"
                                        Margin="0,0,0,5"
                                        Text="{Binding FingerprintHash}"
                                        TextWrapping="Wrap" />

                                    <TextBlock
                                        Grid.Row="9"
                                        Grid.Column="0"
                                        Margin="0,0,10,5"
                                        FontWeight="Bold"
                                        Text="创建时间:" />
                                    <TextBlock
                                        Grid.Row="9"
                                        Grid.Column="1"
                                        Margin="0,0,0,5"
                                        Text="{Binding CreatedAt, StringFormat='yyyy-MM-dd HH:mm:ss'}" />
                                </Grid>
                            </StackPanel>
                        </ScrollViewer>
                    </TabItem>

                    <TabItem Header="指纹报告">
                        <Grid Margin="20">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="*" />
                            </Grid.RowDefinitions>

                            <Button
                                Grid.Row="0"
                                Margin="0,0,0,15"
                                HorizontalAlignment="Left"
                                Command="{Binding GenerateReportCommand}"
                                Style="{StaticResource MaterialDesignRaisedButton}">
                                <Button.Content>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Margin="0,0,5,0" Kind="FileDocument" />
                                        <TextBlock Text="生成报告" />
                                    </StackPanel>
                                </Button.Content>
                            </Button>

                            <ScrollViewer Grid.Row="1">
                                <TextBlock
                                    FontFamily="Consolas"
                                    FontSize="12"
                                    Text="{Binding FingerprintReport}"
                                    TextWrapping="Wrap" />
                            </ScrollViewer>
                        </Grid>
                    </TabItem>
                </TabControl>
            </materialDesign:Card>
        </Grid>

        <!--  状态栏  -->
        <StatusBar Grid.Row="3" Margin="0,10,0,0">
            <StatusBarItem>
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon
                        Margin="0,0,5,0"
                        Kind="Information"
                        Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}" />
                    <TextBlock Text="{Binding StatusMessage}" />
                </StackPanel>
            </StatusBarItem>
            <Separator />
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock Text="{Binding Fingerprints.Count, StringFormat='共 {0} 个指纹'}" />
            </StatusBarItem>
        </StatusBar>
    </Grid>
</UserControl>
