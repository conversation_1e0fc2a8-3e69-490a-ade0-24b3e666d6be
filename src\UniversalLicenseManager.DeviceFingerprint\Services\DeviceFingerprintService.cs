using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UniversalLicenseManager.Core.Models;
using UniversalLicenseManager.Core.Services;
using UniversalLicenseManager.Core.Utilities;
using UniversalLicenseManager.Core.Constants;
using UniversalLicenseManager.Core.Exceptions;

namespace UniversalLicenseManager.DeviceFingerprint.Services
{
    /// <summary>
    /// 设备指纹服务实现
    /// 提供设备硬件信息采集和指纹生成功能
    /// </summary>
    public class DeviceFingerprintService : IDeviceFingerprintService
    {
        private readonly IEncryptionService _encryptionService;
        private readonly ILogService _logService;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="encryptionService">加密服务</param>
        /// <param name="logService">日志服务</param>
        public DeviceFingerprintService(IEncryptionService encryptionService, ILogService logService)
        {
            _encryptionService = encryptionService ?? throw new ArgumentNullException(nameof(encryptionService));
            _logService = logService ?? throw new ArgumentNullException(nameof(logService));
        }

        /// <summary>
        /// 获取CPU序列号
        /// </summary>
        /// <returns>CPU序列号</returns>
        public async Task<string> GetCpuIdAsync()
        {
            try
            {
                _logService.Debug("开始获取CPU序列号");
                var cpuId = await HardwareInfoHelper.GetCpuSerialNumberAsync();
                _logService.Debug($"CPU序列号获取成功: {cpuId}");
                return cpuId;
            }
            catch (Exception ex)
            {
                _logService.Error("获取CPU序列号失败", ex);
                throw new DeviceFingerprintException(ApplicationConstants.DeviceFingerprint.ComponentCPU, "获取CPU序列号失败", ex);
            }
        }

        /// <summary>
        /// 获取主板序列号
        /// </summary>
        /// <returns>主板序列号</returns>
        public async Task<string> GetMotherboardIdAsync()
        {
            try
            {
                _logService.Debug("开始获取主板序列号");
                var motherboardId = await HardwareInfoHelper.GetMotherboardSerialNumberAsync();
                _logService.Debug($"主板序列号获取成功: {motherboardId}");
                return motherboardId;
            }
            catch (Exception ex)
            {
                _logService.Error("获取主板序列号失败", ex);
                throw new DeviceFingerprintException(ApplicationConstants.DeviceFingerprint.ComponentMotherboard, "获取主板序列号失败", ex);
            }
        }

        /// <summary>
        /// 获取硬盘序列号
        /// </summary>
        /// <returns>硬盘序列号</returns>
        public async Task<string> GetHardDiskIdAsync()
        {
            try
            {
                _logService.Debug("开始获取硬盘序列号");
                var hardDiskId = await HardwareInfoHelper.GetHardDiskSerialNumberAsync();
                _logService.Debug($"硬盘序列号获取成功: {hardDiskId}");
                return hardDiskId;
            }
            catch (Exception ex)
            {
                _logService.Error("获取硬盘序列号失败", ex);
                throw new DeviceFingerprintException(ApplicationConstants.DeviceFingerprint.ComponentHardDisk, "获取硬盘序列号失败", ex);
            }
        }

        /// <summary>
        /// 获取MAC地址
        /// </summary>
        /// <returns>MAC地址</returns>
        public async Task<string> GetMacAddressAsync()
        {
            try
            {
                _logService.Debug("开始获取MAC地址");
                var macAddress = await HardwareInfoHelper.GetMacAddressAsync();
                _logService.Debug($"MAC地址获取成功: {macAddress}");
                return macAddress;
            }
            catch (Exception ex)
            {
                _logService.Error("获取MAC地址失败", ex);
                throw new DeviceFingerprintException(ApplicationConstants.DeviceFingerprint.ComponentMacAddress, "获取MAC地址失败", ex);
            }
        }

        /// <summary>
        /// 获取系统UUID
        /// </summary>
        /// <returns>系统UUID</returns>
        public async Task<string> GetSystemUuidAsync()
        {
            try
            {
                _logService.Debug("开始获取系统UUID");
                var systemUuid = await HardwareInfoHelper.GetSystemUuidAsync();
                _logService.Debug($"系统UUID获取成功: {systemUuid}");
                return systemUuid;
            }
            catch (Exception ex)
            {
                _logService.Error("获取系统UUID失败", ex);
                throw new DeviceFingerprintException(ApplicationConstants.DeviceFingerprint.ComponentSystemUUID, "获取系统UUID失败", ex);
            }
        }

        /// <summary>
        /// 获取设备名称
        /// </summary>
        /// <returns>设备名称</returns>
        public string GetDeviceName()
        {
            try
            {
                _logService.Debug("开始获取设备名称");
                var deviceName = HardwareInfoHelper.GetComputerName();
                _logService.Debug($"设备名称获取成功: {deviceName}");
                return deviceName;
            }
            catch (Exception ex)
            {
                _logService.Error("获取设备名称失败", ex);
                throw new DeviceFingerprintException(ApplicationConstants.DeviceFingerprint.ComponentComputerName, "获取设备名称失败", ex);
            }
        }

        /// <summary>
        /// 获取所有硬件信息
        /// </summary>
        /// <returns>硬件信息字典</returns>
        public async Task<Dictionary<string, string>> GetAllHardwareInfoAsync()
        {
            try
            {
                _logService.Info("开始获取所有硬件信息");
                var hardwareInfo = await HardwareInfoHelper.GetAllHardwareInfoAsync();
                _logService.Info($"硬件信息获取成功，共获取到 {hardwareInfo.Count} 项信息");
                return hardwareInfo;
            }
            catch (Exception ex)
            {
                _logService.Error("获取硬件信息失败", ex);
                throw new DeviceFingerprintException("获取硬件信息失败", ex);
            }
        }

        /// <summary>
        /// 生成设备指纹
        /// </summary>
        /// <param name="strategy">生成策略</param>
        /// <param name="algorithm">哈希算法</param>
        /// <returns>设备指纹对象</returns>
        public async Task<Core.Models.DeviceFingerprint> GenerateFingerprintAsync(FingerprintStrategy strategy = FingerprintStrategy.AllHardware, string algorithm = "SHA256")
        {
            try
            {
                _logService.Info($"开始生成设备指纹，策略: {strategy}, 算法: {algorithm}");

                var fingerprint = new Core.Models.DeviceFingerprint
                {
                    DeviceName = GetDeviceName(),
                    Algorithm = algorithm,
                    CreatedAt = DateTime.Now
                };

                // 根据策略获取硬件信息
                await PopulateHardwareInfoByStrategy(fingerprint, strategy);

                // 生成指纹字符串并计算哈希
                var fingerprintString = fingerprint.GenerateFingerprintString();
                fingerprint.FingerprintHash = _encryptionService.ComputeHash(fingerprintString, algorithm);

                _logService.Info($"设备指纹生成成功: {fingerprint.FingerprintHash}");
                return fingerprint;
            }
            catch (Exception ex)
            {
                _logService.Error("生成设备指纹失败", ex);
                throw new DeviceFingerprintException("生成设备指纹失败", ex);
            }
        }

        /// <summary>
        /// 使用自定义组合生成设备指纹
        /// </summary>
        /// <param name="includeComponents">要包含的硬件组件</param>
        /// <param name="algorithm">哈希算法</param>
        /// <returns>设备指纹对象</returns>
        public async Task<Core.Models.DeviceFingerprint> GenerateCustomFingerprintAsync(List<string> includeComponents, string algorithm = "SHA256")
        {
            try
            {
                _logService.Info($"开始生成自定义设备指纹，组件: [{string.Join(", ", includeComponents)}], 算法: {algorithm}");

                var fingerprint = new Core.Models.DeviceFingerprint
                {
                    DeviceName = GetDeviceName(),
                    Algorithm = algorithm,
                    CreatedAt = DateTime.Now
                };

                // 根据指定组件获取硬件信息
                await PopulateHardwareInfoByComponents(fingerprint, includeComponents);

                // 生成指纹字符串并计算哈希
                var fingerprintString = fingerprint.GenerateFingerprintString();
                fingerprint.FingerprintHash = _encryptionService.ComputeHash(fingerprintString, algorithm);

                _logService.Info($"自定义设备指纹生成成功: {fingerprint.FingerprintHash}");
                return fingerprint;
            }
            catch (Exception ex)
            {
                _logService.Error("生成自定义设备指纹失败", ex);
                throw new DeviceFingerprintException("生成自定义设备指纹失败", ex);
            }
        }

        /// <summary>
        /// 验证设备指纹
        /// </summary>
        /// <param name="fingerprint">要验证的指纹</param>
        /// <param name="strategy">验证策略</param>
        /// <returns>是否匹配</returns>
        public async Task<bool> ValidateFingerprintAsync(Core.Models.DeviceFingerprint fingerprint, FingerprintStrategy strategy = FingerprintStrategy.AllHardware)
        {
            try
            {
                _logService.Info($"开始验证设备指纹: {fingerprint.FingerprintHash}");

                // 生成当前设备的指纹
                var currentFingerprint = await GenerateFingerprintAsync(strategy, fingerprint.Algorithm);

                // 比较指纹哈希
                var isMatch = string.Equals(fingerprint.FingerprintHash, currentFingerprint.FingerprintHash, StringComparison.OrdinalIgnoreCase);

                _logService.Info($"设备指纹验证结果: {(isMatch ? "匹配" : "不匹配")}");
                return isMatch;
            }
            catch (Exception ex)
            {
                _logService.Error("验证设备指纹失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 比较两个设备指纹
        /// </summary>
        /// <param name="fingerprint1">指纹1</param>
        /// <param name="fingerprint2">指纹2</param>
        /// <returns>相似度（0-1之间）</returns>
        public double CompareFingerprintSimilarity(Core.Models.DeviceFingerprint fingerprint1, Core.Models.DeviceFingerprint fingerprint2)
        {
            try
            {
                if (fingerprint1 == null || fingerprint2 == null)
                    return 0.0;

                var components = new[]
                {
                    (fingerprint1.CpuId, fingerprint2.CpuId),
                    (fingerprint1.MotherboardId, fingerprint2.MotherboardId),
                    (fingerprint1.HardDiskId, fingerprint2.HardDiskId),
                    (fingerprint1.MacAddress, fingerprint2.MacAddress),
                    (fingerprint1.SystemUuid, fingerprint2.SystemUuid)
                };

                var matchCount = components.Count(c => string.Equals(c.Item1, c.Item2, StringComparison.OrdinalIgnoreCase));
                var totalCount = components.Length;

                var similarity = (double)matchCount / totalCount;
                _logService.Debug($"指纹相似度计算结果: {similarity:P2}");

                return similarity;
            }
            catch (Exception ex)
            {
                _logService.Error("比较设备指纹相似度失败", ex);
                return 0.0;
            }
        }

        /// <summary>
        /// 根据策略填充硬件信息
        /// </summary>
        /// <param name="fingerprint">设备指纹对象</param>
        /// <param name="strategy">生成策略</param>
        private async Task PopulateHardwareInfoByStrategy(Core.Models.DeviceFingerprint fingerprint, FingerprintStrategy strategy)
        {
            switch (strategy)
            {
                case FingerprintStrategy.CpuOnly:
                    fingerprint.CpuId = await GetCpuIdAsync();
                    break;

                case FingerprintStrategy.MotherboardOnly:
                    fingerprint.MotherboardId = await GetMotherboardIdAsync();
                    break;

                case FingerprintStrategy.HardDiskOnly:
                    fingerprint.HardDiskId = await GetHardDiskIdAsync();
                    break;

                case FingerprintStrategy.MacAddressOnly:
                    fingerprint.MacAddress = await GetMacAddressAsync();
                    break;

                case FingerprintStrategy.CpuAndMotherboard:
                    fingerprint.CpuId = await GetCpuIdAsync();
                    fingerprint.MotherboardId = await GetMotherboardIdAsync();
                    break;

                case FingerprintStrategy.CpuAndHardDisk:
                    fingerprint.CpuId = await GetCpuIdAsync();
                    fingerprint.HardDiskId = await GetHardDiskIdAsync();
                    break;

                case FingerprintStrategy.MotherboardAndHardDisk:
                    fingerprint.MotherboardId = await GetMotherboardIdAsync();
                    fingerprint.HardDiskId = await GetHardDiskIdAsync();
                    break;

                case FingerprintStrategy.AllHardware:
                default:
                    fingerprint.CpuId = await GetCpuIdAsync();
                    fingerprint.MotherboardId = await GetMotherboardIdAsync();
                    fingerprint.HardDiskId = await GetHardDiskIdAsync();
                    fingerprint.MacAddress = await GetMacAddressAsync();
                    fingerprint.SystemUuid = await GetSystemUuidAsync();
                    break;
            }
        }

        /// <summary>
        /// 根据指定组件填充硬件信息
        /// </summary>
        /// <param name="fingerprint">设备指纹对象</param>
        /// <param name="components">硬件组件列表</param>
        private async Task PopulateHardwareInfoByComponents(Core.Models.DeviceFingerprint fingerprint, List<string> components)
        {
            foreach (var component in components)
            {
                switch (component.ToUpper())
                {
                    case ApplicationConstants.DeviceFingerprint.ComponentCPU:
                        fingerprint.CpuId = await GetCpuIdAsync();
                        break;

                    case ApplicationConstants.DeviceFingerprint.ComponentMotherboard:
                        fingerprint.MotherboardId = await GetMotherboardIdAsync();
                        break;

                    case ApplicationConstants.DeviceFingerprint.ComponentHardDisk:
                        fingerprint.HardDiskId = await GetHardDiskIdAsync();
                        break;

                    case ApplicationConstants.DeviceFingerprint.ComponentMacAddress:
                        fingerprint.MacAddress = await GetMacAddressAsync();
                        break;

                    case ApplicationConstants.DeviceFingerprint.ComponentSystemUUID:
                        fingerprint.SystemUuid = await GetSystemUuidAsync();
                        break;
                }
            }
        }

        /// <summary>
        /// 获取当前设备的指纹字符串
        /// </summary>
        /// <param name="strategy">生成策略</param>
        /// <returns>指纹字符串</returns>
        public async Task<string> GetCurrentDeviceFingerprintStringAsync(FingerprintStrategy strategy = FingerprintStrategy.AllHardware)
        {
            try
            {
                var fingerprint = await GenerateFingerprintAsync(strategy);
                return fingerprint.FingerprintHash;
            }
            catch (Exception ex)
            {
                _logService.Error("获取当前设备指纹字符串失败", ex);
                throw new DeviceFingerprintException("获取当前设备指纹字符串失败", ex);
            }
        }

        /// <summary>
        /// 检查硬件组件是否可用
        /// </summary>
        /// <param name="component">硬件组件名称</param>
        /// <returns>是否可用</returns>
        public async Task<bool> IsHardwareComponentAvailableAsync(string component)
        {
            try
            {
                return await HardwareInfoHelper.IsHardwareComponentAvailableAsync(component);
            }
            catch (Exception ex)
            {
                _logService.Warning($"检查硬件组件 {component} 可用性失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取可用的硬件组件列表
        /// </summary>
        /// <returns>硬件组件列表</returns>
        public async Task<List<string>> GetAvailableHardwareComponentsAsync()
        {
            try
            {
                return await HardwareInfoHelper.GetAvailableHardwareComponentsAsync();
            }
            catch (Exception ex)
            {
                _logService.Error("获取可用硬件组件列表失败", ex);
                return new List<string>();
            }
        }

        /// <summary>
        /// 导出设备指纹为JSON
        /// </summary>
        /// <param name="fingerprint">设备指纹</param>
        /// <returns>JSON字符串</returns>
        public string ExportFingerprintToJson(Core.Models.DeviceFingerprint fingerprint)
        {
            try
            {
                return JsonHelper.Serialize(fingerprint);
            }
            catch (Exception ex)
            {
                _logService.Error("导出设备指纹为JSON失败", ex);
                throw new DeviceFingerprintException("导出设备指纹为JSON失败", ex);
            }
        }

        /// <summary>
        /// 从JSON导入设备指纹
        /// </summary>
        /// <param name="json">JSON字符串</param>
        /// <returns>设备指纹对象</returns>
        public Core.Models.DeviceFingerprint ImportFingerprintFromJson(string json)
        {
            try
            {
                return JsonHelper.Deserialize<Core.Models.DeviceFingerprint>(json);
            }
            catch (Exception ex)
            {
                _logService.Error("从JSON导入设备指纹失败", ex);
                throw new DeviceFingerprintException("从JSON导入设备指纹失败", ex);
            }
        }

        /// <summary>
        /// 生成设备指纹报告
        /// </summary>
        /// <param name="fingerprint">设备指纹</param>
        /// <returns>详细报告</returns>
        public async Task<string> GenerateFingerprintReportAsync(Core.Models.DeviceFingerprint fingerprint)
        {
            try
            {
                var report = new System.Text.StringBuilder();
                report.AppendLine("=== 设备指纹详细报告 ===");
                report.AppendLine($"生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                report.AppendLine();

                report.AppendLine("基本信息:");
                report.AppendLine($"  设备名称: {fingerprint.DeviceName}");
                report.AppendLine($"  指纹ID: {fingerprint.Id}");
                report.AppendLine($"  创建时间: {fingerprint.CreatedAt:yyyy-MM-dd HH:mm:ss}");
                report.AppendLine($"  哈希算法: {fingerprint.Algorithm}");
                report.AppendLine($"  指纹哈希: {fingerprint.FingerprintHash}");
                report.AppendLine($"  状态: {(fingerprint.IsActive ? "激活" : "未激活")}");
                report.AppendLine();

                report.AppendLine("硬件信息:");
                report.AppendLine($"  CPU序列号: {fingerprint.CpuId ?? "未获取"}");
                report.AppendLine($"  主板序列号: {fingerprint.MotherboardId ?? "未获取"}");
                report.AppendLine($"  硬盘序列号: {fingerprint.HardDiskId ?? "未获取"}");
                report.AppendLine($"  MAC地址: {fingerprint.MacAddress ?? "未获取"}");
                report.AppendLine($"  系统UUID: {fingerprint.SystemUuid ?? "未获取"}");
                report.AppendLine();

                // 验证当前设备匹配情况
                var isValid = await ValidateFingerprintAsync(fingerprint);
                report.AppendLine("验证结果:");
                report.AppendLine($"  与当前设备匹配: {(isValid ? "是" : "否")}");
                report.AppendLine();

                // 可用硬件组件
                var availableComponents = await GetAvailableHardwareComponentsAsync();
                report.AppendLine("当前设备可用硬件组件:");
                foreach (var component in availableComponents)
                {
                    report.AppendLine($"  - {component}");
                }

                if (!string.IsNullOrEmpty(fingerprint.Remarks))
                {
                    report.AppendLine();
                    report.AppendLine("备注:");
                    report.AppendLine($"  {fingerprint.Remarks}");
                }

                return report.ToString();
            }
            catch (Exception ex)
            {
                _logService.Error("生成设备指纹报告失败", ex);
                throw new DeviceFingerprintException("生成设备指纹报告失败", ex);
            }
        }
    }
}
