# 通用软件注册机桌面应用程序 (Universal License Manager)

## 项目概括
本项目旨在开发一个基于C# .NET和WPF技术栈的通用软件授权管理平台。该应用程序采用Prism框架实现模块化架构，严格遵循MVVM设计模式，为后续开发的软件产品提供统一的License管理解决方案。系统支持离线验证机制，具备高度的可复用性和扩展性。

## 技术选型
- **主要编程语言**: C# .NET 6.0+
- **UI框架**: WPF (Windows Presentation Foundation)
- **MVVM框架**: Prism 8.1+
- **架构模式**: 严格MVVM模式，业务逻辑完全在ViewModel中实现
- **导航系统**: Prism区域导航 (Region Navigation)
- **依赖注入**: Prism内置DI容器
- **加密算法**: RSA、AES、SHA256等
- **数据序列化**: JSON.NET (Newtonsoft.Json)
- **配置管理**: JSON配置文件
- **版本控制**: Git
- **单元测试**: MSTest / xUnit
- **文档生成**: XML文档注释 + DocFX

## 项目结构 / 模块划分
```
UniversalLicenseManager/
├── src/
│   ├── UniversalLicenseManager.Shell/          # 主Shell应用程序
│   │   ├── App.xaml                            # 应用程序入口
│   │   ├── MainWindow.xaml                     # 主窗口
│   │   ├── ViewModels/                         # Shell ViewModels
│   │   └── Views/                              # Shell Views
│   ├── UniversalLicenseManager.Core/           # 核心业务逻辑模块
│   │   ├── Models/                             # 数据模型
│   │   ├── Services/                           # 核心服务接口与实现
│   │   ├── Encryption/                         # 加密算法实现
│   │   └── Utilities/                          # 工具类
│   ├── UniversalLicenseManager.DeviceFingerprint/ # 设备指纹模块
│   │   ├── ViewModels/                         # 设备指纹相关ViewModels
│   │   ├── Views/                              # 设备指纹管理界面
│   │   └── Services/                           # 设备指纹生成服务
│   ├── UniversalLicenseManager.LicenseGeneration/ # License生成模块
│   │   ├── ViewModels/                         # License生成ViewModels
│   │   ├── Views/                              # License生成界面
│   │   └── Services/                           # License生成与验证服务
│   ├── UniversalLicenseManager.Configuration/  # 配置管理模块
│   │   ├── ViewModels/                         # 配置管理ViewModels
│   │   ├── Views/                              # 配置编辑界面
│   │   └── Services/                           # JSON配置服务
│   ├── UniversalLicenseManager.DeveloperTools/ # 开发者工具模块
│   │   ├── ViewModels/                         # 开发者工具ViewModels
│   │   ├── Views/                              # 代码生成与文档界面
│   │   └── Services/                           # 代码生成服务
│   └── UniversalLicenseManager.SDK/            # 客户端集成SDK
│       ├── LicenseValidator.cs                 # License验证器
│       ├── DeviceFingerprintGenerator.cs       # 设备指纹生成器
│       └── Models/                             # SDK数据模型
├── tests/                                      # 单元测试项目
├── docs/                                       # 项目文档
├── templates/                                  # JSON配置模板
└── samples/                                    # 集成示例代码
```

## 核心功能 / 模块详解
### 1. 设备指纹生成与管理模块 (DeviceFingerprint)
- **硬件信息采集**: CPU序列号、主板序列号、硬盘序列号、MAC地址、系统UUID等
- **指纹算法支持**: MD5、SHA1、SHA256多种哈希算法
- **组合策略配置**: 支持自定义硬件信息组合方式
- **指纹验证**: 设备指纹有效性检查和重复性验证

### 2. License生成与验证系统 (LicenseGeneration)
- **RSA密钥对管理**: 自动生成和管理公私钥对
- **License文件生成**: 基于设备指纹和授权配置生成加密License
- **离线验证机制**: 无需网络连接的License有效性验证
- **过期时间管理**: 支持永久授权和时间限制授权
- **License状态跟踪**: 已生成License的管理和状态监控

### 3. 自定义授权配置系统 (Configuration)
- **JSON模板编辑器**: 可视化编辑授权类型和功能模块配置
- **授权类型定义**: 试用版、标准版、专业版等多层级授权
- **功能模块权限**: 细粒度的功能模块启用/禁用控制
- **配置文件管理**: 配置模板的导入、导出、版本管理

### 4. 开发者辅助工具 (DeveloperTools)
- **集成代码生成**: 自动生成客户端集成所需的验证代码
- **API文档生成**: 生成完整的SDK使用文档和API参考
- **示例项目生成**: 创建包含License验证的示例应用程序
- **最佳实践指南**: 输出集成最佳实践和常见问题解决方案

## 数据模型设计
### DeviceFingerprint (设备指纹)
```csharp
{
  "Id": "string (GUID)",
  "DeviceName": "string",
  "CpuId": "string",
  "MotherboardId": "string",
  "HardDiskId": "string",
  "MacAddress": "string",
  "SystemUuid": "string",
  "FingerprintHash": "string",
  "Algorithm": "string (MD5/SHA1/SHA256)",
  "CreatedAt": "DateTime",
  "IsActive": "bool"
}
```

### LicenseInfo (License信息)
```csharp
{
  "Id": "string (GUID)",
  "DeviceFingerprintId": "string",
  "LicenseKey": "string",
  "ProductName": "string",
  "LicenseType": "string",
  "AuthorizedFeatures": "List<string>",
  "IssueDate": "DateTime",
  "ExpirationDate": "DateTime?",
  "IsActive": "bool",
  "EncryptedData": "string"
}
```

### AuthorizationConfig (授权配置)
```csharp
{
  "ConfigId": "string (GUID)",
  "ProductName": "string",
  "LicenseTypes": [
    {
      "TypeName": "string",
      "DisplayName": "string",
      "Features": ["string"],
      "Duration": "int? (days)",
      "MaxDevices": "int?"
    }
  ],
  "AvailableFeatures": [
    {
      "FeatureId": "string",
      "FeatureName": "string",
      "Description": "string",
      "IsRequired": "bool"
    }
  ]
}
```

## 技术实现细节
[本部分将在开发过程中逐步填充各模块的具体实现方案、关键算法、API设计等详细信息]

## 开发状态跟踪
| 模块/功能                    | 状态   | 负责人 | 计划完成日期 | 实际完成日期 | 备注与链接 |
|------------------------------|--------|--------|--------------|--------------|------------|
| 项目架构搭建与Shell应用      | 未开始 | AI     | 2024-01-15   |              |            |
| 核心业务逻辑模块(Core)       | 未开始 | AI     | 2024-01-18   |              |            |
| 设备指纹生成与管理模块       | 未开始 | AI     | 2024-01-22   |              |            |
| License生成与验证系统        | 未开始 | AI     | 2024-01-25   |              |            |
| 自定义授权配置系统           | 未开始 | AI     | 2024-01-28   |              |            |
| 开发者辅助工具模块           | 未开始 | AI     | 2024-01-30   |              |            |
| 客户端集成SDK开发            | 未开始 | AI     | 2024-02-02   |              |            |
| 单元测试与集成测试           | 未开始 | AI     | 2024-02-05   |              |            |
| 文档编写与示例代码           | 未开始 | AI     | 2024-02-08   |              |            |

## 代码检查与问题记录
[本部分用于记录代码检查结果和开发过程中遇到的问题及其解决方案]

## 环境设置与运行指南
### 开发环境要求
- Visual Studio 2022 或 Visual Studio Code
- .NET 6.0 SDK 或更高版本
- Windows 10/11 操作系统

### 依赖包管理
```xml
<!-- 主要NuGet包依赖 -->
<PackageReference Include="Prism.Wpf" Version="8.1.97" />
<PackageReference Include="Prism.DryIoc" Version="8.1.97" />
<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
<PackageReference Include="System.Management" Version="7.0.0" />
```

### 运行指南
[将在项目开发完成后提供详细的编译、运行和部署指南]

## 集成指南与SDK使用
[本部分将包含完整的客户端集成文档、SDK使用示例和最佳实践指南]
