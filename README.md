# 通用软件注册机桌面应用程序 (Universal License Manager)

## 项目概括
本项目旨在开发一个基于C# .NET和WPF技术栈的通用软件授权管理平台。该应用程序采用Prism框架实现模块化架构，严格遵循MVVM设计模式，为后续开发的软件产品提供统一的License管理解决方案。系统支持离线验证机制，具备高度的可复用性和扩展性。

## 技术选型
- **主要编程语言**: C# .NET 6.0+
- **UI框架**: WPF (Windows Presentation Foundation)
- **MVVM框架**: Prism 8.1+
- **架构模式**: 严格MVVM模式，业务逻辑完全在ViewModel中实现
- **导航系统**: Prism区域导航 (Region Navigation)
- **依赖注入**: Prism内置DI容器
- **加密算法**: RSA、AES、SHA256等
- **数据序列化**: JSON.NET (Newtonsoft.Json)
- **配置管理**: JSON配置文件
- **版本控制**: Git
- **单元测试**: MSTest / xUnit
- **文档生成**: XML文档注释 + DocFX

## 项目结构 / 模块划分
```
UniversalLicenseManager/
├── src/
│   ├── UniversalLicenseManager.Shell/          # 主Shell应用程序
│   │   ├── App.xaml                            # 应用程序入口
│   │   ├── MainWindow.xaml                     # 主窗口
│   │   ├── ViewModels/                         # Shell ViewModels
│   │   └── Views/                              # Shell Views
│   ├── UniversalLicenseManager.Core/           # 核心业务逻辑模块
│   │   ├── Models/                             # 数据模型
│   │   ├── Services/                           # 核心服务接口与实现
│   │   ├── Encryption/                         # 加密算法实现
│   │   └── Utilities/                          # 工具类
│   ├── UniversalLicenseManager.DeviceFingerprint/ # 设备指纹模块
│   │   ├── ViewModels/                         # 设备指纹相关ViewModels
│   │   ├── Views/                              # 设备指纹管理界面
│   │   └── Services/                           # 设备指纹生成服务
│   ├── UniversalLicenseManager.LicenseGeneration/ # License生成模块
│   │   ├── ViewModels/                         # License生成ViewModels
│   │   ├── Views/                              # License生成界面
│   │   └── Services/                           # License生成与验证服务
│   ├── UniversalLicenseManager.Configuration/  # 配置管理模块
│   │   ├── ViewModels/                         # 配置管理ViewModels
│   │   ├── Views/                              # 配置编辑界面
│   │   └── Services/                           # JSON配置服务
│   ├── UniversalLicenseManager.DeveloperTools/ # 开发者工具模块
│   │   ├── ViewModels/                         # 开发者工具ViewModels
│   │   ├── Views/                              # 代码生成与文档界面
│   │   └── Services/                           # 代码生成服务
│   └── UniversalLicenseManager.SDK/            # 客户端集成SDK
│       ├── LicenseValidator.cs                 # License验证器
│       ├── DeviceFingerprintGenerator.cs       # 设备指纹生成器
│       └── Models/                             # SDK数据模型
├── tests/                                      # 单元测试项目
├── docs/                                       # 项目文档
├── templates/                                  # JSON配置模板
└── samples/                                    # 集成示例代码
```

## 核心功能 / 模块详解
### 1. 设备指纹生成与管理模块 (DeviceFingerprint)
- **硬件信息采集**: CPU序列号、主板序列号、硬盘序列号、MAC地址、系统UUID等
- **指纹算法支持**: MD5、SHA1、SHA256多种哈希算法
- **组合策略配置**: 支持自定义硬件信息组合方式
- **指纹验证**: 设备指纹有效性检查和重复性验证

### 2. License生成与验证系统 (LicenseGeneration)
- **RSA密钥对管理**: 自动生成和管理公私钥对
- **License文件生成**: 基于设备指纹和授权配置生成加密License
- **离线验证机制**: 无需网络连接的License有效性验证
- **过期时间管理**: 支持永久授权和时间限制授权
- **License状态跟踪**: 已生成License的管理和状态监控

### 3. 自定义授权配置系统 (Configuration)
- **JSON模板编辑器**: 可视化编辑授权类型和功能模块配置
- **授权类型定义**: 试用版、标准版、专业版等多层级授权
- **功能模块权限**: 细粒度的功能模块启用/禁用控制
- **配置文件管理**: 配置模板的导入、导出、版本管理

### 4. 开发者辅助工具 (DeveloperTools)
- **集成代码生成**: 自动生成客户端集成所需的验证代码
- **API文档生成**: 生成完整的SDK使用文档和API参考
- **示例项目生成**: 创建包含License验证的示例应用程序
- **最佳实践指南**: 输出集成最佳实践和常见问题解决方案

## 数据模型设计
### DeviceFingerprint (设备指纹)
```csharp
{
  "Id": "string (GUID)",
  "DeviceName": "string",
  "CpuId": "string",
  "MotherboardId": "string",
  "HardDiskId": "string",
  "MacAddress": "string",
  "SystemUuid": "string",
  "FingerprintHash": "string",
  "Algorithm": "string (MD5/SHA1/SHA256)",
  "CreatedAt": "DateTime",
  "IsActive": "bool"
}
```

### LicenseInfo (License信息)
```csharp
{
  "Id": "string (GUID)",
  "DeviceFingerprintId": "string",
  "LicenseKey": "string",
  "ProductName": "string",
  "LicenseType": "string",
  "AuthorizedFeatures": "List<string>",
  "IssueDate": "DateTime",
  "ExpirationDate": "DateTime?",
  "IsActive": "bool",
  "EncryptedData": "string"
}
```

### AuthorizationConfig (授权配置)
```csharp
{
  "ConfigId": "string (GUID)",
  "ProductName": "string",
  "LicenseTypes": [
    {
      "TypeName": "string",
      "DisplayName": "string",
      "Features": ["string"],
      "Duration": "int? (days)",
      "MaxDevices": "int?"
    }
  ],
  "AvailableFeatures": [
    {
      "FeatureId": "string",
      "FeatureName": "string",
      "Description": "string",
      "IsRequired": "bool"
    }
  ]
}
```

## 技术实现细节

### 项目架构搭建与Shell应用 ✅
**实现时间**: 2024-01-15
**技术方案**:
- **解决方案结构**: 采用多项目解决方案，按功能模块分离
- **Shell应用程序**: 基于Prism.Wpf框架，实现模块化架构
- **UI设计**: 使用Material Design主题，提供现代化用户界面
- **导航系统**: 基于Prism区域导航，支持模块间的松耦合导航

**关键组件**:
1. **App.xaml/App.xaml.cs**: 应用程序入口，配置Prism容器和模块目录
2. **MainWindow**: 主窗口界面，包含自定义标题栏、导航菜单和内容区域
3. **MainWindowViewModel**: 主窗口业务逻辑，处理时间显示等功能
4. **CoreModule**: 核心模块，提供基础服务和通用功能

**项目结构**:
```
UniversalLicenseManager.sln          # 解决方案文件
├── src/
│   ├── UniversalLicenseManager.Shell/     # Shell应用程序 ✅
│   │   ├── App.xaml                       # 应用程序入口
│   │   ├── Views/MainWindow.xaml          # 主窗口界面
│   │   └── ViewModels/MainWindowViewModel.cs # 主窗口ViewModel
│   ├── UniversalLicenseManager.Core/      # 核心模块 ✅
│   │   ├── Models/                        # 数据模型
│   │   │   ├── DeviceFingerprint.cs       # 设备指纹模型
│   │   │   └── LicenseInfo.cs             # License信息模型
│   │   └── CoreModule.cs                  # 核心模块定义
│   └── [其他模块目录已创建，待开发]
```

**MVVM架构实现**:
- 严格遵循MVVM模式，View不包含业务逻辑
- 使用Prism的ViewModelLocator自动绑定ViewModel
- 通过ICommand和数据绑定实现UI交互

**Material Design集成**:
- 集成MaterialDesignThemes.Wpf包
- 自定义窗口样式，去除默认标题栏
- 实现现代化的导航菜单和内容区域布局

### 核心业务逻辑模块(Core) ✅
**实现时间**: 2024-01-18
**技术方案**:
- **完整数据模型**: 设备指纹、License信息、授权配置、验证结果等核心数据模型
- **服务接口设计**: 加密、文件、日志、配置、设备指纹、License等服务接口
- **工具类库**: 硬件信息获取、JSON序列化、常量定义等工具类
- **异常处理**: 完整的异常体系，包含各种业务异常类型

**核心组件**:
1. **数据模型层**:
   - `DeviceFingerprint`: 设备指纹数据模型，支持多种硬件信息组合
   - `LicenseInfo`: License信息模型，包含完整的授权信息
   - `AuthorizationConfig`: 授权配置模型，支持灵活的产品授权定义
   - `LicenseValidationResult`: License验证结果模型，详细的验证状态和错误信息

2. **服务接口层**:
   - `IEncryptionService`: 加密服务，支持RSA、AES、数字签名等
   - `IDeviceFingerprintService`: 设备指纹服务，硬件信息采集和指纹生成
   - `ILicenseService`: License服务，License生成、验证、管理
   - `IFileService`: 文件服务，异步文件操作
   - `ILogService`: 日志服务，分级日志记录
   - `IConfigurationService`: 配置服务，JSON配置管理

3. **工具类库**:
   - `HardwareInfoHelper`: 硬件信息获取工具，支持CPU、主板、硬盘等信息
   - `JsonHelper`: JSON序列化工具，统一的序列化/反序列化功能
   - `ApplicationConstants`: 应用程序常量定义

4. **异常处理**:
   - `LicenseManagerException`: 基础异常类
   - `LicenseValidationException`: License验证异常
   - `DeviceFingerprintException`: 设备指纹异常
   - `EncryptionException`: 加密异常
   - `ConfigurationException`: 配置异常

**技术特性**:
- **模块化设计**: 清晰的分层架构，高内聚低耦合
- **异步支持**: 所有I/O操作支持异步模式
- **错误处理**: 完善的异常体系和错误代码定义
- **扩展性**: 接口驱动设计，易于扩展和测试
- **安全性**: 多重加密算法支持，数字签名验证

## 开发状态跟踪
| 模块/功能                    | 状态   | 负责人 | 计划完成日期 | 实际完成日期 | 备注与链接 |
|------------------------------|--------|--------|--------------|--------------|------------|
| 项目架构搭建与Shell应用      | 已完成 | AI     | 2024-01-15   | 2024-01-15   | [详见技术实现](#项目架构搭建与shell应用-) |
| 核心业务逻辑模块(Core)       | 已完成 | AI     | 2024-01-18   | 2024-01-18   | [详见技术实现](#核心业务逻辑模块core-) |
| 设备指纹生成与管理模块       | 未开始 | AI     | 2024-01-22   |              |            |
| License生成与验证系统        | 未开始 | AI     | 2024-01-25   |              |            |
| 自定义授权配置系统           | 未开始 | AI     | 2024-01-28   |              |            |
| 开发者辅助工具模块           | 未开始 | AI     | 2024-01-30   |              |            |
| 客户端集成SDK开发            | 未开始 | AI     | 2024-02-02   |              |            |
| 单元测试与集成测试           | 未开始 | AI     | 2024-02-05   |              |            |
| 文档编写与示例代码           | 未开始 | AI     | 2024-02-08   |              |            |

## 代码检查与问题记录
[本部分用于记录代码检查结果和开发过程中遇到的问题及其解决方案]

## 环境设置与运行指南
### 开发环境要求
- Visual Studio 2022 或 Visual Studio Code
- .NET 6.0 SDK 或更高版本
- Windows 10/11 操作系统

### 依赖包管理
```xml
<!-- 主要NuGet包依赖 -->
<PackageReference Include="Prism.Wpf" Version="8.1.97" />
<PackageReference Include="Prism.DryIoc" Version="8.1.97" />
<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
<PackageReference Include="System.Management" Version="7.0.0" />
```

### 运行指南
[将在项目开发完成后提供详细的编译、运行和部署指南]

## 集成指南与SDK使用
[本部分将包含完整的客户端集成文档、SDK使用示例和最佳实践指南]
